using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;//Please add references
namespace DMS.BLL
{
    /// <summary>
    /// 数据访问类:dms_files
    /// </summary>
    public partial class dms_files
    {
        public dms_files()
        {
        }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_files");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(DMS.Model.dms_files model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into dms_files(");
            strSql.Append("Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5)");
            strSql.Append(" values (");
            strSql.Append("@Id,@FileName,@FileType,@FilePath,@Creator,@CreateTime,@DirectoryId,@DirectoryPath,@Memo,@ColumnId,@ColumnPath,@IsOpen,@FileFormat,@FileSize,@FileStatus,@FileMD5)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@FileName", SqlDbType.NVarChar,100),
                    new SqlParameter("@FileType", SqlDbType.Int,4),
                    new SqlParameter("@FilePath", SqlDbType.NVarChar,500),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4),
                    new SqlParameter("@DirectoryPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@Memo", SqlDbType.NVarChar,200),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@IsOpen", SqlDbType.Int,4),
                    new SqlParameter("@FileFormat", SqlDbType.NVarChar,10),
                    new SqlParameter("@FileSize", SqlDbType.Int,4),
                    new SqlParameter("@FileStatus", SqlDbType.Int,4),
                    new SqlParameter("@FileMD5", SqlDbType.NVarChar,50)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.FileName;
            parameters[2].Value = model.FileType;
            parameters[3].Value = model.FilePath;
            parameters[4].Value = model.Creator;
            parameters[5].Value = model.CreateTime;
            parameters[6].Value = model.DirectoryId;
            parameters[7].Value = model.DirectoryPath;
            parameters[8].Value = model.Memo;
            parameters[9].Value = model.ColumnId;
            parameters[10].Value = model.ColumnPath;
            parameters[11].Value = model.IsOpen;
            parameters[12].Value = model.FileFormat;
            parameters[13].Value = model.FileSize;
            parameters[14].Value = model.FileStatus;
            parameters[15].Value = model.FileMD5;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(DMS.Model.dms_files model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update dms_files set ");
            strSql.Append("FileName=@FileName,");
            strSql.Append("FileType=@FileType,");
            strSql.Append("FilePath=@FilePath,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("DirectoryId=@DirectoryId,");
            strSql.Append("DirectoryPath=@DirectoryPath,");
            strSql.Append("Memo=@Memo,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("IsOpen=@IsOpen,");
            strSql.Append("FileFormat=@FileFormat,");
            strSql.Append("FileSize=@FileSize");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@FileName", SqlDbType.NVarChar,100),
                    new SqlParameter("@FileType", SqlDbType.Int,4),
                    new SqlParameter("@FilePath", SqlDbType.NVarChar,500),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4),
                    new SqlParameter("@DirectoryPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@Memo", SqlDbType.NVarChar,200),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
                    new SqlParameter("@IsOpen", SqlDbType.Int,4),
                    new SqlParameter("@FileFormat", SqlDbType.NVarChar,10),
                    new SqlParameter("@FileSize", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.FileName;
            parameters[1].Value = model.FileType;
            parameters[2].Value = model.FilePath;
            parameters[3].Value = model.Creator;
            parameters[4].Value = model.CreateTime;
            parameters[5].Value = model.DirectoryId;
            parameters[6].Value = model.DirectoryPath;
            parameters[7].Value = model.Memo;
            parameters[8].Value = model.ColumnId;
            parameters[9].Value = model.ColumnPath;
            parameters[10].Value = model.IsOpen;
            parameters[11].Value = model.FileFormat;
            parameters[12].Value = model.FileSize;
            parameters[13].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_files ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_files ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_files GetModel(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 from dms_files ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            DMS.Model.dms_files model = new DMS.Model.dms_files();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_files DataRowToModel(DataRow row)
        {
            DMS.Model.dms_files model = new DMS.Model.dms_files();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["FileName"] != null)
                {
                    model.FileName = row["FileName"].ToString();
                }
                if (row["FileType"] != null && row["FileType"].ToString() != "")
                {
                    model.FileType = int.Parse(row["FileType"].ToString());
                }
                if (row["FilePath"] != null)
                {
                    model.FilePath = row["FilePath"].ToString();
                }
                if (row["Creator"] != null && row["Creator"].ToString() != "")
                {
                    model.Creator = new Guid(row["Creator"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["DirectoryId"] != null && row["DirectoryId"].ToString() != "")
                {
                    model.DirectoryId = int.Parse(row["DirectoryId"].ToString());
                }
                if (row["DirectoryPath"] != null)
                {
                    model.DirectoryPath = row["DirectoryPath"].ToString();
                }
                if (row["Memo"] != null)
                {
                    model.Memo = row["Memo"].ToString();
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["IsOpen"] != null && row["IsOpen"].ToString() != "")
                {
                    model.IsOpen = int.Parse(row["IsOpen"].ToString());
                }
                if (row["FileFormat"] != null)
                {
                    model.FileFormat = row["FileFormat"].ToString();
                }
                if (row["FileSize"] != null && row["FileSize"].ToString() != "")
                {
                    model.FileSize = int.Parse(row["FileSize"].ToString());
                }
                if (row["FileStatus"] != null && row["FileStatus"].ToString() != "")
                {
                    model.FileStatus = int.Parse(row["FileStatus"].ToString());
                }
                if (row["FileMD5"] != null)
                {
                    model.FileMD5 = row["FileMD5"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 ");
            strSql.Append(" FROM dms_files ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 ");
            strSql.Append(" FROM dms_files ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM dms_files ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from dms_files T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "dms_files";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="directory">目录id</param>
        /// <returns></returns>
        public DataSet GetList(int directory)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 from dms_files ");
            strSql.Append(" where DirectoryId=@DirectoryId order by CreateTime");
            SqlParameter[] parameters = {
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4)};
            parameters[0].Value = directory;

            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="directory">目录id</param>
        /// <param name="isOpen">是否公开</param>
        /// <param name="userId">用户id</param>
        /// <returns></returns>
        public DataSet GetList(int directory, int isOpen, Guid userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 from dms_files ");
            strSql.Append(" where DirectoryId=@DirectoryId and (IsOpen=@IsOpen or Creator=@Creator) order by CreateTime");
            SqlParameter[] parameters = {
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4),
                    new SqlParameter("@IsOpen", SqlDbType.Int,4),
                    new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = directory;
            parameters[1].Value = isOpen;
            parameters[2].Value = userId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取上传文件类型
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public int getFileType(string fileType)
        {
            fileType = fileType.ToLower();
            int type = (int)CommonEnum.dms_FileType.Others;
            //txt文档
            if ("*.txt".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Txt;
            }
            //word文档
            if ("*.doc,*.docx".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Word;
            }
            //execl文档
            if ("*.xls,*.xlsx".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Excel;
            }
            //ppt文档
            if ("*.pptx,*.ppt".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Ppt;
            }
            //图片文件
            if ("*.png,*.jpeg,*.gif,*.tiff,*.raw,*.bmp,*.swf".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Image;
            }
            //视频文件
            if (".avi,.wma,.rmvb,.rm,.flash,.mp4,.mid,3gp".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Video;
            }
            //音频文件
            if (" *.wav,*.mp3,*.wma,*.ogg,*.ape,*.acc,".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Voice;
            }
            //压缩文件
            if ("*.rar,*.zip,*.7z,*.cab,*.tar,*.jar,*.iso".IndexOf(fileType) > -1)
            {
                type = (int)CommonEnum.dms_FileType.Rar;
            }
            return type;
        }

        ///// <summary>
        ///// 获取文件列表
        ///// </summary>
        ///// <param name="directory">目录id</param>
        ///// <param name="top">多少条</param>
        ///// <returns></returns>
        //public DataSet GetList(int directory, int top)
        //{
        //    StringBuilder strSql = new StringBuilder();
        //    strSql.Append("select top " + top + " Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen from dms_files ");
        //    strSql.Append(" where DirectoryId=@DirectoryId ");
        //    SqlParameter[] parameters = {
        //            new SqlParameter("@DirectoryId", SqlDbType.Int,4)};
        //    parameters[0].Value = directory;

        //    return DbHelperSQL.Query(strSql.ToString(), parameters);
        //}

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="fileName">搜索的文件名</param>
        /// <param name="start">开始日期</param>
        /// <param name="end">结束日期</param>
        /// <returns></returns>
        public DataSet GetList(string directoryPath, string fileName, DateTime start, DateTime end)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 from dms_files ");
            strSql.Append(" where DirectoryPath+'|' like @DirectoryPath+'|%' ");
            List<SqlParameter> parameters = new List<SqlParameter>();
            parameters.Add(new SqlParameter("@DirectoryPath", SqlDbType.NVarChar, 500)
            {
                Value = directoryPath
            });
            // 判断是否有文件名搜索
            if (!string.IsNullOrEmpty(fileName))
            {
                strSql.Append(" and FileName like '%'+@FileName+'%'");
                parameters.Add(new SqlParameter("@FileName", SqlDbType.NVarChar, 100)
                {
                    Value = fileName
                });
            }
            // 判断开始时间是否有效
            if (start != DateTime.MinValue)
            {
                strSql.Append(" and CreateTime>=@StartTime");
                parameters.Add(new SqlParameter("@StartTime", SqlDbType.DateTime, 8)
                {
                    Value = DateTime.Parse(start.ToString("yyyy-MM-dd 00:00:00"))
                });
            }
            // 判断结束时间是否有效
            if (end != DateTime.MinValue)
            {
                strSql.Append(" and CreateTime<=@EndTime");
                parameters.Add(new SqlParameter("@EndTime", SqlDbType.DateTime, 8)
                {
                    Value = DateTime.Parse(end.ToString("yyyy-MM-dd 23:59:59"))
                });
            }
            strSql.Append(" order by CreateTime");
            return DbHelperSQL.Query(strSql.ToString(), parameters.ToArray());
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="fileName">搜索的文件名</param>
        /// <param name="start">开始日期</param>
        /// <param name="end">结束日期</param>
        /// <param name="isOpen">是否公开</param>
        /// <param name="userId">用户id</param>
        /// <returns></returns>
        public DataSet GetList(string directoryPath, string fileName, DateTime start, DateTime end, int isOpen, Guid userId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 from dms_files ");
            strSql.Append(" where DirectoryPath+'|' like @DirectoryPath+'|%' and (IsOpen=@IsOpen or Creator=@Creator)");
            List<SqlParameter> parameters = new List<SqlParameter>();
            parameters.Add(new SqlParameter("@DirectoryPath", SqlDbType.NVarChar, 500)
            {
                Value = directoryPath
            });
            parameters.Add(new SqlParameter("@IsOpen", SqlDbType.Int, 4)
            {
                Value = isOpen
            });
            parameters.Add(new SqlParameter("@Creator", SqlDbType.UniqueIdentifier, 16)
            {
                Value = userId
            });
            // 判断是否有文件名搜索
            if (!string.IsNullOrEmpty(fileName))
            {
                strSql.Append(" and FileName like '%'+@FileName+'%'");
                parameters.Add(new SqlParameter("@FileName", SqlDbType.NVarChar, 100)
                {
                    Value = fileName
                });
            }
            // 判断开始时间是否有效
            if (start != DateTime.MinValue)
            {
                strSql.Append(" and CreateTime>=@StartTime");
                parameters.Add(new SqlParameter("@StartTime", SqlDbType.DateTime, 8)
                {
                    Value = DateTime.Parse(start.ToString("yyyy-MM-dd 00:00:00"))
                });
            }
            // 判断结束时间是否有效
            if (end != DateTime.MinValue)
            {
                strSql.Append(" and CreateTime<=@EndTime");
                parameters.Add(new SqlParameter("@EndTime", SqlDbType.DateTime, 8)
                {
                    Value = DateTime.Parse(end.ToString("yyyy-MM-dd 23:59:59"))
                });
            }
            strSql.Append(" order by CreateTime");
            return DbHelperSQL.Query(strSql.ToString(), parameters.ToArray());
        }

        /// <summary>
        /// 获取文件类型限制
        /// </summary>
        /// <param name="columnPath">地区路径</param>
        /// <param name="domain">域名</param>
        /// <returns></returns>
        public string GetFileTypeLimit(int columnId, string domain)
        {
            string limit = "*.rar,*.zip,*.png,*.jpeg,*.gif,*.doc,*.xls,*.docx,*.mp4";
            BLL.JC_Config bll_config = new BLL.JC_Config();
            Model.JC_Config model_config = bll_config.GetModel(columnId, domain);
            if (model_config != null && !string.IsNullOrEmpty(model_config.UploadFileTypeLimit))
            {
                limit = model_config.UploadFileTypeLimit;
            }
            return limit;
        }

        /// <summary>
        /// 获取文件类型限制
        /// </summary>
        /// <param name="columnPath">地区路径</param>
        /// <param name="domain">域名</param>
        /// <returns></returns>
        public string GetFileSizeLimit(int columnId, string domain)
        {
            string limit = "120M";
            BLL.JC_Config bll_config = new BLL.JC_Config();
            Model.JC_Config model_config = bll_config.GetModel(columnId, domain);
            if (model_config != null && model_config.UploadFileSizeLimit > 0)
            {
                limit = model_config.UploadFileSizeLimit + "M";
            }
            return limit;
        }

        /// <summary>
        /// 检查文件名是否存在
        /// </summary>
        /// <param name="directoryId">目录ID</param>
        /// <param name="fileName">文件名称</param>
        /// <returns>是否存在</returns>
        public bool ExistsFileName(int directoryId, string fileName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_files");
            strSql.Append(" where DirectoryId=@DirectoryId AND FileName=@FileName");
            SqlParameter[] parameters = {
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4),
                    new SqlParameter("@FileName", SqlDbType.NVarChar,100)
            };
            parameters[0].Value = directoryId;
            parameters[1].Value = fileName;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 根据MD5查找文件
        /// </summary>
        /// <param name="fileMD5">文件MD5值</param>
        /// <returns>文件信息</returns>
        public DMS.Model.dms_files GetModelByMD5(string fileMD5)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,FileName,FileType,FilePath,Creator,CreateTime,DirectoryId,DirectoryPath,Memo,ColumnId,ColumnPath,IsOpen,FileFormat,FileSize,FileStatus,FileMD5 from dms_files ");
            strSql.Append(" where FileMD5=@FileMD5");
            SqlParameter[] parameters = {
                    new SqlParameter("@FileMD5", SqlDbType.NVarChar,50)
            };
            parameters[0].Value = fileMD5;

            DMS.Model.dms_files model = new DMS.Model.dms_files();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 检查MD5是否存在
        /// </summary>
        /// <param name="fileMD5">文件MD5值</param>
        /// <returns>是否存在</returns>
        public bool ExistsByMD5(string fileMD5, int excludeDirectoryId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_files");
            strSql.Append(" where FileMD5=@FileMD5 and DirectoryId<>@DirectoryId");
            SqlParameter[] parameters = {
                    new SqlParameter("@FileMD5", SqlDbType.NVarChar,50),
                    new SqlParameter("@DirectoryId",SqlDbType.Int,4)
            };
            parameters[0].Value = fileMD5;
            parameters[1].Value = excludeDirectoryId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 更新文件状态
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="status">文件状态</param>
        /// <returns>是否成功</returns>
        public bool UpdateFileStatus(Guid fileId, int status)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update dms_files set ");
            strSql.Append("FileStatus=@FileStatus");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
                    new SqlParameter("@FileStatus", SqlDbType.Int,4),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)
            };
            parameters[0].Value = status;
            parameters[1].Value = fileId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        #endregion  ExtensionMethod
    }
}

