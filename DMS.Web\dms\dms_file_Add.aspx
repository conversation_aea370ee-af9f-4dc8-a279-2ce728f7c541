﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_file_Add.aspx.cs" Inherits="DMS.Web.dms.dms_file_Add" ValidateRequest="false" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>上传文件</title>
    <link href="/js/webuploader/webuploader.css" rel="stylesheet" />
    <script src="/js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="/js/webuploader/webuploader.min.js" type="text/javascript"></script>
    <script src="/js/webuploader/webuploader.helper.js" type="text/javascript"></script>
    <script src="/admin/js/Common.js" type="text/javascript"></script>
    <link href="/js/layer/skin/layer.css" rel="stylesheet" />
    <script src="/js/layer/layer.js" type="text/javascript"></script>
    <link href="js/jQuery.filer/css/jquery.filer.css" type="text/css" rel="stylesheet" />
    <link href="js/jQuery.filer/css/themes/jquery.filer-dragdropbox-theme.css" type="text/css" rel="stylesheet" />
    <script src="js/jQuery.filer/js/jquery.filer.js"></script>
    <link href="css/dms_file_Add.css" rel="stylesheet" />
    <script>
        function CloseBox() {
            parent.layer.close(parent.layer.getFrameIndex(window.name));
        };
        function showMessage(success, content, ico) {
            var _index = parent.layer.alert(content, {
                icon: ico,
                shade: ['0.1', '#000'],
                time: success == "true" ? 1000 : 0,
                end: function () {
                    if (success == "true") {
                        parent.refreshData();
                        CloseBox();
                    }

                }
            });
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <div id="floatHead" class="content-tab-wrap" style="height: 42px;">
                <div class="content-tab" style="position: static; top: 52px;">
                    <div class="content-tab-ul-wrap">
                        <ul>
                            <li><a class="selected" href="javascript:void(0);">单个上传</a></li>
                            <li id="liBatch"><a href="javascript:void(0);">批量上传</a></li>
                            <asp:HiddenField runat="server" ID="hidUploadType" Value="0" />
                        </ul>
                    </div>
                </div>
            </div>
            <div class="tab-content" style="">
                <div id="list" class="infoTable margin-bottom-bar">
                    <dl>
                        <dt><span class="red">*</span>上传文件</dt>
                        <dd>
                            <input runat="server" type="hidden" name="hfldFileJSON" id="hfldFileJSON" />
                            <div class="uploader" style="line-height: normal;"></div>
                            <div class="photo-list">
                                <ul></ul>
                            </div>
                            <p class="append">支持上传的文件类型<span runat="server" id="spanFileType"></span></p>
                        </dd>
                    </dl>
                    <dl>
                        <dt>
                            <span class="red">*</span>文件标题
                        </dt>
                        <dd>
                            <asp:TextBox ID="txtTitle" runat="server" Width="200px"></asp:TextBox>
                        </dd>
                    </dl>
                    <dl>
                        <dt>文件描述</dt>
                        <dd>
                            <div class="act content" style="width: 99%;">
                                <asp:TextBox ID="txtContents" runat="server" Width="100%" Height="150px" TextMode="MultiLine"></asp:TextBox>
                                <asp:HiddenField ID="hidContent" runat="server" />
                            </div>
                        </dd>
                    </dl>
                    <dl>
                        <dt>是否公开</dt>
                        <dd>
                            <div class="rule-single-checkbox single-checkbox chkIsOpen">
                                <a href="javascript:void(0);"><i class="off">否</i><i class="on">是</i></a>
                                <asp:CheckBox ID="chkIsOpen" runat="server" Text="是" Style="display: none;" />
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
            <div id="dvBatch" class="tab-content" style="display: none">

                <input type="file" name="files[]" id="filer_input" multiple="multiple" />
                <dl style="padding-top: 10px">
                    <dt>是否公开</dt>
                    <dd>
                        <div class="rule-single-checkbox single-checkbox chkIsOpen">
                            <a href="javascript:void(0);"><i class="off">否</i><i class="on">是</i></a>
                            <asp:CheckBox ID="chkIsOpenMult" runat="server" Text="是" Style="display: none;" />
                        </div>
                    </dd>
                </dl>
                <asp:HiddenField runat="server" ID="hidSchool" />
                <asp:HiddenField runat="server" ID="hidDirectoryId" />
            </div>
            <div class="bottom-bar">
                <input id="btnAdd" type="button" value="保存" class="btnGreen" />
                <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btnGreen" OnClick="btnSave_Click" Style="display: none" />&nbsp;
                <input id="btnClose" type="button" value="关闭" class="btnGray" onclick="CloseBox()" />
            </div>
            <asp:HiddenField runat="server" ID="hidFileType" />
            <asp:HiddenField runat="server" ID="hidFileSize" />
        </div>
    </form>
</body>
</html>
<script>
    var fileSize;
    var fileType;
    var _currentIndex;
    var varObj = [];// 定义
    $(function () {
        fileSize = $("#hidFileSize").val();
        fileType = $("#spanFileType").text();

        //引用上传附件
        var fileList = "";
        if ($('#hfldFileJSON').val() != "" && $('#hfldFileJSON').val() != "[]") {
            fileList = JSON.parse("[" + $('#hfldFileJSON').val() + "]");
        }
        $('.uploader').InitUploader({
            btntext: "选择文件",
            multiple: false,
            fileNumLimit: 1,
            water: true,
            thumbnail: true,
            filetypes: fileType.replaceAll("*.", ""),
            filesize: fileSize.replace("M", "") * 1024,
            sendurl: "/dms/ajax/UploadFile.ashx?directoryId=" + $('#hidDirectoryId').val(),
            swf: "js/webuploader/uploader.swf",
            alreadyFiles: fileList
        });
        //是否置顶
        if ($("#chkIsOpen").attr("checked") == "checked")
            $(".rule-single-checkbox.single-checkbox.chkIsOpen a").addClass("selected");

        //是否置顶
        $(".rule-single-checkbox.single-checkbox.chkIsOpen").click(function () {
            if ($(this).find("a").toggleClass("selected").is(".selected")) {
                $("#chkIsOpen").attr("checked", true);
                $("#chkIsOpenMult").attr("checked", true);

            }
            else {
                $("#chkIsOpen").attr("checked", false);
                $("#chkIsOpenMult").attr("checked", false);
            }
        });

        //菜单栏
        $("#floatHead ul li a").click(function () {
            $(this).parent().siblings().find("a").removeClass("selected");
            $(this).addClass("selected");
            $(".tab-content").hide();
            $(".tab-content:eq(" + $(this).parent().index() + ")").show();
            $("#hidUploadType").val($(this).parent().index());
        });

        //是否添加
        if ("<%=IsAdd%>" == "0") {
            $("#dvBatch").hide();
            $("#liBatch").hide();
        }
    })
    //单个文件上传-自定义生成预览图片
    function addImage(targetObj, originalSrc, thumbSrc, size, ext, data) {
        //插入到相册UL里面
        var exts = "*.jpg;*.jpeg;*.png;*.gif,*.bmp";
        var newLi = $('<li>'
            + '<input type="hidden" name="hid_photo_name" value="' + size + '|' + originalSrc + '|' + ext + '|' + data + '" />'
            + '<div class="img-box" onclick="setFocusImg(this);"></div>'
            + '<a href="javascript:;");">删除</a>'
            + '</li>').children('a').click(function () { delImg(this, targetObj) }).end();
        var imgBox = newLi.find("div");
        if (exts.indexOf("." + ext) == -1) {
            imgBox.removeAttr("onclick");
            imgBox.append('<div class= "fileImg" src = "' + thumbSrc + '" ><div>' + ext + '</div></div >');
        } else {
            imgBox.append('<img src="' + thumbSrc + '" bigsrc="' + originalSrc + '" layer-src="' + originalSrc + '" />');
        }
        newLi.appendTo(targetObj.siblings(".photo-list").children("ul"));
    }
    //添加
    $("#btnAdd").click(function () {
        if ($("#hidUploadType").val() == "0") {
            // 单个上传
            $("input[name=hid_photo_name]").each(function () {
                var fileInfo = $(this).val().split('|');
                varObj[varObj.length] = {
                    Size: fileInfo[0],
                    Path: fileInfo[1],
                    Format: '.' + fileInfo[2],
                    UploadDate: new Date(),
                    Name: fileInfo[3]
                }
            });
            $("#hfldFileJSON").val(JSON.stringify(varObj));
            if ($("#hfldFileJSON").val() == "[]" && $("#hfldFileJSON").val() != "") {
                layer.msg('请选择需要上传的文件');
                return false;
            }
            if ($('#txtTitle').val() == "") {
                layer.msg('文件标题不能为空');
                return false;
            }
            $("#btnSave").click();
        } else {
            // 批量上传 - 取消保存操作，文件已在上传时直接保存
            layer.msg('批量上传的文件已自动保存，无需手动保存操作！', {
                icon: 1,
                time: 2000,
                end: function () {
                    if (parent && parent.refreshData) {
                        parent.refreshData();
                    }
                    CloseBox();
                }
            });
        }

    })
    //预览图片
    function FileImg(object, ImgName) {
        var _src = $(object).attr("src");
        var json = {
            "title": "", //相册标题
            "id": 123, //相册id
            "start": 0, //初始显示的图片序号，默认0
            "data": [   //相册包含的图片，数组格式
                {
                    "alt": ImgName,
                    "pid": 666, //图片id
                    "src": _src, //原图地址
                    "thumb": _src //缩略图地址
                }
            ]
        }
        layer.photos({
            photos: json, anim: 5, shade: 0.2
        });
    }


    //批量上传
    var _file = [];
    $(function () {
        var _fileType = "";
        if ($("#hidFileType").val() != "") {
            _fileType = $("#hidFileType").val().split(",");
        }
        $('#filer_input').filer({
            maxSize: fileSize.substring(0, fileSize.length - 1),//上传文件的最大尺寸，单位MB
            changeInput: '<div class="jFiler-input-dragDrop"><div class="jFiler-input-inner"><div class="jFiler-input-icon"><i class="icon-jfi-cloud-up-o"></i></div><div class="jFiler-input-text"><h3>将文件拖拽到此区域</h3> <span style="display:inline-block; margin: 2px 0;">或</span></div><a class="jFiler-input-choose-btn blue">选择文件</a></div><a>按住Ctrl可多选照片</a><p>支持上传的文件类型' + fileType + '</a></p>',
            showThumbs: true,
            theme: "dragdropbox",
            extensions: _fileType,
            templates: {
                box: '<ul class="jFiler-items-list jFiler-items-grid"></ul>',
                item: '<li class="jFiler-item">\
                            <div class="jFiler-item-container">\
                                <div class="jFiler-item-inner">\
                                    <div class="jFiler-item-thumb">\
                                        <div class="jFiler-item-status"></div>\
                                        <div class="jFiler-item-info">\
                                            <span class="jFiler-item-title"><b title="{{fi-name}}">{{fi-name | limitTo: 25}}</b></span>\
                                            <span class="jFiler-item-others">{{fi-size2}}</span>\
                                        </div>\
                                        {{fi-image}}\
                                    </div>\
                                    <div class="jFiler-item-assets jFiler-row">\
                                        <ul class="list-inline pull-left">\
                                            <li>{{fi-progressBar}}</li>\
                                        </ul>\
                                        <ul class="list-inline pull-right">\
                                            <li><a class="icon-jfi-trash jFiler-item-trash-action"></a></li>\
                                        </ul>\
                                    </div>\
                                </div>\
                            </div>\
                        </li>',
                itemAppend: '<li class="jFiler-item">\
                        <div class="jFiler-item-container">\
                            <div class="jFiler-item-inner">\
                                <div class="jFiler-item-thumb">\
                                    <div class="jFiler-item-status"></div>\
                                    <div class="jFiler-item-info">\
                                        <span class="jFiler-item-title"><b title="{{fi-name}}">{{fi-name | limitTo: 25}}</b></span>\
                                        <span class="jFiler-item-others">{{fi-size2}}</span>\
                                    </div>\
                                    {{fi-image}}\
                                </div>\
                                <div class="jFiler-item-assets jFiler-row">\
                                    <ul class="list-inline pull-left">\
                                        <li><span class="jFiler-item-others">{{fi-icon}}</span></li>\
                                    </ul>\
                                    <ul class="list-inline pull-right">\
                                        <li><a class="icon-jfi-trash jFiler-item-trash-action"></a></li>\
                                    </ul>\
                                </div>\
                            </div>\
                        </div>\
                    </li>',
                progressBar: '<div class="bar"></div>',
                itemAppendToEnd: false,
                removeConfirmation: true,
                _selectors: {
                    list: '.jFiler-items-list',
                    item: '.jFiler-item',
                    progressBar: '.bar',
                    remove: '.jFiler-item-trash-action'
                }
            },
            dragDrop: {
                dragEnter: null,
                dragLeave: null,
                drop: null,
            },
            uploadFile: {
                url: "ajax/uploadfiles.ashx",
                data: { Method: 'upload_file', directoryId: $("#hidDirectoryId").val() },
                type: 'POST',
                enctype: 'multipart/form-data',
                beforeSend: function () { },
                dataType: 'json',
                success: function (data, el) {
                    var parent = el.find(".jFiler-jProgressBar").parent();
                    el.find(".jFiler-jProgressBar").fadeOut("slow", function () {
                        $("<div class=\"jFiler-item-others text-success\"><i class=\"icon-jfi-check-circle\"></i> 上传成功</div>").hide().appendTo(parent).fadeIn("slow");
                    });

                    // 直接上传成功，不需要保存操作
                    console.log("文件上传成功:", data);
                },
                error: function (el, xhr, status, error) {
                    var parent = el.find(".jFiler-jProgressBar").parent();
                    el.find(".jFiler-jProgressBar").fadeOut("slow", function () {
                        $("<div class=\"jFiler-item-others text-error\"><i class=\"icon-jfi-minus-circle\"></i> 上传失败</div>").hide().appendTo(parent).fadeIn("slow");
                    });
                    console.log("文件上传失败:", error);
                },
                statusCode: null,
                onProgress: null,
                onComplete: function (listEl, parentEl, newInputEl, inputEl, jFiler) {
                    // 所有文件上传完成后的回调
                    console.log("所有文件上传完成");

                    // 延迟刷新父页面
                    setTimeout(function () {
                        if (parent && parent.refreshData) {
                            parent.refreshData();
                        }
                        CloseBox();
                    }, 1000);
                }
            },
            files: _file,
            onRemove: function (itemEl, file) {
                $.post('ajax/uploadfiles.ashx', { file: file.name, Method: 'remove_file', directoryId: $("#hidDirectoryId").val() });
            },
            captions: {
                button: "Choose Files",
                feedback: "Choose files To Upload",
                feedback2: "files were chosen",
                drop: "Drop file here to Upload",
                removeConfirmation: "你确定要删除吗？",
                errors: {
                    filesLimit: "Only {{fi-limit}} files are allowed to be uploaded.",
                    filesType: "请上传符合规定的类型" + fileType,
                    filesSize: "{{fi-name}} 文件太大！请上传不超过{{fi-maxSize}}MB的文件。",
                    filesSizeAll: "Files you've choosed are too large! Please upload files up to {{fi-maxSize}} MB."
                }
            }
        });
    });

</script>

