﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{08FE8E2C-2C21-4ED3-B6B3-052486BB7E1A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DMS.Web</RootNamespace>
    <AssemblyName>DMS.Web</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <Use64BitIISExpress />
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AspNetPager">
      <HintPath>..\lib\AspNetPager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FluentFTP, Version=53.0.1.0, Culture=neutral, PublicKeyToken=f4af092b1d8df44f, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentFTP.53.0.1\lib\net462\FluentFTP.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=4.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\lib\Newtonsoft.Json.DLL</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="XYArea, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\课后服务系统\KHS\sourcecode\XYArea\bin\Debug\XYArea.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YunEdu.Authority">
      <HintPath>..\lib\YunEdu.Authority.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YunEdu.BLL, Version=6.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\lib\YunEdu.BLL.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YunEdu.Common, Version=6.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\lib\YunEdu.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YunEdu.Model, Version=6.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\lib\YunEdu.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="dms\css\dms_file_Add.css" />
    <Content Include="dms\dms_config.aspx" />
    <Content Include="dms\dms_config_edit.aspx" />
    <Content Include="dms\dms_download.aspx" />
    <Content Include="dms\dms_file_Add.aspx" />
    <Content Include="dms\dms_file_Auth.aspx" />
    <Content Include="dms\dms_file_manage.aspx" />
    <Content Include="dms\FtpTest.aspx" />
    <Content Include="dms\images\file.png" />
    <Content Include="dms\images\folder.png" />
    <Content Include="dms\js\dms_file_manage.js" />
    <Content Include="dms\js\jQuery.filer\assets\fonts\jquery.filer-icons\jquery-filer-preview.html" />
    <Content Include="dms\js\jQuery.filer\assets\fonts\jquery.filer-icons\jquery-filer.css" />
    <Content Include="dms\js\jQuery.filer\assets\fonts\jquery.filer-icons\jquery-filer.svg" />
    <Content Include="dms\js\jQuery.filer\css\jquery-filer.css" />
    <Content Include="dms\js\jQuery.filer\css\jquery.filer.css" />
    <Content Include="dms\js\jQuery.filer\css\themes\jquery.filer-dragdropbox-theme.css" />
    <Content Include="dms\js\jQuery.filer\js\jquery.filer.js" />
    <Content Include="dms\js\jquery.uploadify-3.1.js" />
    <Content Include="dms\js\spark-md5.min.js" />
    <Content Include="dms\js\UploadFile.js" />
    <Content Include="dms\js\webuploader.helper.file.js" />
    <Content Include="dms\js\webuploader.helper.js" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="dms\ajax\dms_file_manage.ashx.cs">
      <DependentUpon>dms_file_manage.ashx</DependentUpon>
    </Compile>
    <Compile Include="dms\ajax\uploader.ashx.cs">
      <DependentUpon>uploader.ashx</DependentUpon>
    </Compile>
    <Compile Include="dms\ajax\UploadFile.ashx.cs">
      <DependentUpon>UploadFile.ashx</DependentUpon>
    </Compile>
    <Compile Include="dms\ajax\UploadFiles.ashx.cs">
      <DependentUpon>UploadFiles.ashx</DependentUpon>
    </Compile>
    <Compile Include="dms\dms_config.aspx.cs">
      <DependentUpon>dms_config.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\dms_config.aspx.designer.cs">
      <DependentUpon>dms_config.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\dms_config_edit.aspx.cs">
      <DependentUpon>dms_config_edit.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\dms_config_edit.aspx.designer.cs">
      <DependentUpon>dms_config_edit.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\dms_download.aspx.cs">
      <DependentUpon>dms_download.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\dms_download.aspx.designer.cs">
      <DependentUpon>dms_download.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\dms_file_Add.aspx.cs">
      <DependentUpon>dms_file_Add.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\dms_file_Add.aspx.designer.cs">
      <DependentUpon>dms_file_Add.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\dms_file_Auth.aspx.cs">
      <DependentUpon>dms_file_Auth.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\dms_file_Auth.aspx.designer.cs">
      <DependentUpon>dms_file_Auth.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\dms_file_manage.aspx.cs">
      <DependentUpon>dms_file_manage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\dms_file_manage.aspx.designer.cs">
      <DependentUpon>dms_file_manage.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\FtpHelper.cs" />
    <Compile Include="dms\FtpHelperFluentFTP.cs" />
    <Compile Include="dms\FtpHelper_Fix_Test.cs" />
    <Compile Include="dms\FtpHelper_Usage_Example.cs" />
    <Compile Include="dms\FtpTest.aspx.cs">
      <DependentUpon>FtpTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="dms\FtpTest.aspx.designer.cs">
      <DependentUpon>FtpTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="dms\UploadFileResResult.cs" />
    <Compile Include="dms\UploadFileInfo.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="dms\ajax\dms_file_manage.ashx" />
    <Content Include="dms\ajax\UploadFile.ashx" />
    <Content Include="dms\js\jQuery.filer\assets\fonts\jquery.filer-icons\jquery-filer.eot" />
    <Content Include="dms\js\jQuery.filer\assets\fonts\jquery.filer-icons\jquery-filer.ttf" />
    <Content Include="dms\js\jQuery.filer\assets\fonts\jquery.filer-icons\jquery-filer.woff" />
    <Content Include="dms\ajax\UploadFiles.ashx" />
    <Content Include="dms\ajax\uploader.ashx" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\DMS.pubxml" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="..\DMS.BLL\DMS.BLL.csproj">
      <Project>{29a07c36-6576-427f-8508-be231ec51909}</Project>
      <Name>DMS.BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\DMS.Model\DMS.Model.csproj">
      <Project>{c502cf10-6a02-426a-8759-4be222e4c3a9}</Project>
      <Name>DMS.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>3942</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:3881/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>