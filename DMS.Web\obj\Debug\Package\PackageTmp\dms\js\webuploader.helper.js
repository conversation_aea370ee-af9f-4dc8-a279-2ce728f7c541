﻿$(function () {
    //初始化绑定默认的属性
    $.upLoadDefaults = $.upLoadDefaults || {};
    $.upLoadDefaults.property = {
        fileNumLimit: null,// 文件上传数量限制
        multiple: false, //是否多文件
        water: false, //是否加水印
        thumbnail: false, //是否生成缩略图
        sendurl: null, //发送地址
        callback: null,//回调函数
        filetypes: "jpg,jpge,png,gif", //文件类型
        filesize: "2048", //文件大小
        btntext: "浏览...", //上传按钮的文字
        swf: null, //SWF上传控件相对地址
        alreadyFiles: [],// 已存在的文件
        chunked: true, //是否启用分片上传，默认启用
        chunkSize: 2, //分片大小(MB)，默认2MB
        directoryId: 1, //目标目录ID
    };
    //初始化上传控件
    $.fn.InitUploader = function (b) {
        var fun = function (parentObj) {
            // 文件数量
            parentObj.prototype = {
                fileNum: 0
            };
            var p = $.extend({}, $.upLoadDefaults.property, b || {});
            if (p.alreadyFiles && p.alreadyFiles.length > 0) {
                parentObj.prototype.fileNum += p.alreadyFiles.length;
                $.each(p.alreadyFiles, function (i, data) {
                    addImage(parentObj, data.path, data.thumb, data.size, data.ext, data.name, data.md5);
                })
            }
            var btnObj = $('<div class="upload-btn">' + p.btntext + '</div>').appendTo(parentObj);
            //初始化属性 - 使用新的分片上传接口
            var isChunkUpload = p.chunked !== false; // 默认启用分片上传

            if (isChunkUpload) {
                // 使用分片上传接口
                if (p.sendurl.indexOf("?") > -1)
                    p.sendurl += "&action=upload";
                else
                    p.sendurl += "?action=upload";
            } else {
                // 使用传统上传接口
                if (p.sendurl.indexOf("?") > -1)
                    p.sendurl += "&action=UpLoadFile";
                else
                    p.sendurl += "?action=UpLoadFile";

                if (p.water) {
                    p.sendurl += "&IsWater=1";
                }
                if (p.thumbnail) {
                    p.sendurl += "&IsThumbnail=1";
                }
                if (!p.multiple) {
                    p.sendurl += "&DelFilePath=" + parentObj.siblings(".upload-path").val();
                }
            }

            //初始化WebUploader
            var uploaderConfig = {
                auto: false, //改为手动上传，以便计算MD5
                swf: p.swf, //SWF路径
                server: p.sendurl, //上传地址
                pick: {
                    id: btnObj,
                    multiple: p.multiple
                },
                accept: {  // 允许上传文件类型
                    extensions: p.filetypes
                },
                compress: false,//不启用压缩
                resize: false,//尺寸不改变
                fileSingleSizeLimit: p.filesize * 1024, //文件大小
                fileVal: isChunkUpload ? 'file' : 'Filedata' //上传域的名称
            };

            // 分片上传配置
            if (isChunkUpload) {
                uploaderConfig.chunked = true;
                uploaderConfig.chunkSize = (p.chunkSize || 2) * 1024 * 1024; // 默认2MB分片
                uploaderConfig.threads = 1; // 串行上传，避免并发问题
                uploaderConfig.formData = {
                    'directoryId': p.directoryId // 目录ID
                };
            } else {
                uploaderConfig.chunked = false;
                uploaderConfig.threads = 3;
                uploaderConfig.formData = {
                    'DelFilePath': '' //定义参数
                };
            }

            var uploader = WebUploader.create(uploaderConfig);

            //当validate不通过时，会以派送错误事件的形式通知
            uploader.on('error', function (type) {
                switch (type) {
                    case 'Q_EXCEED_NUM_LIMIT':
                        alert("错误：上传文件数量过多！");
                        break;
                    case 'Q_EXCEED_SIZE_LIMIT':
                        alert("错误：文件总大小超出限制！");
                        break;
                    case 'F_EXCEED_SIZE':
                        alert("错误：文件大小超出限制！");
                        break;
                    case 'Q_TYPE_DENIED':
                        alert("错误：禁止上传该类型文件！");
                        break;
                    case 'F_DUPLICATE':
                        alert("错误：请勿重复上传该文件！");
                        break;
                    default:
                        alert('错误代码：' + type);
                        break;
                }
            });

            //文件上传过程中创建进度条实时显示
            uploader.on('beforeFileQueued', function (file) {
                if (p.multiple && p.fileNumLimit) {
                    if (parentObj.prototype.fileNum >= p.fileNumLimit) {
                        alert("错误：最多只能上传" + p.fileNumLimit + "个文件！");
                        return false;
                    }
                }
            });

            //当有文件添加进来的时候
            uploader.on('fileQueued', function (file) {
                //防止重复创建
                if (parentObj.children(".upload-progress").length == 0) {
                    //创建进度条
                    var fileProgressObj = $('<div class="upload-progress"></div>').appendTo(parentObj);
                    var progressText = $('<span class="txt">正在准备上传...</span>').appendTo(fileProgressObj);
                    var progressBar = $('<span class="bar"><b></b></span>').appendTo(fileProgressObj);
                    var progressCancel = $('<a class="close" title="取消上传">关闭</a>').appendTo(fileProgressObj);
                    //绑定点击事件
                    progressCancel.click(function () {
                        uploader.cancelFile(file);
                        fileProgressObj.remove();
                    });
                }

                if (isChunkUpload) {
                    // 分片上传：先计算MD5，再检查状态
                    handleChunkUpload(file, uploader, parentObj, p);
                } else {
                    // 传统上传：直接上传
                    if (!p.multiple) {
                        uploader.options.formData.DelFilePath = parentObj.siblings(".upload-path").val();
                    }
                    uploader.upload(file);
                }
            });

            //文件上传过程中创建进度条实时显示
            uploader.on('uploadProgress', function (file, percentage) {
                var progressObj = parentObj.children(".upload-progress");
                progressObj.children(".txt").html(file.name);
                progressObj.find(".bar b").width(percentage * 100 + "%");
            });

            //当文件上传出错时触发
            uploader.on('uploadError', function (file, reason) {
                uploader.removeFile(file); //从队列中移除
                alert(file.name + "上传失败，错误代码：" + reason);
            });

            //当文件上传成功时触发
            uploader.on('uploadSuccess', function (file, data) {
                var progressObj = parentObj.children(".upload-progress");

                if (isChunkUpload) {
                    // 分片上传响应处理
                    if (data.success) {
                        if (data.data && data.data.completed) {
                            // 文件上传完成
                            var fileInfo = data.data;
                            progressObj.children(".txt").html("上传成功：" + file.name);

                            //如果是单文件上传，则清空之前图片列表
                            if (!p.multiple) {
                                parentObj.siblings(".photo-list").children("ul").children("li").remove();
                                addImage(parentObj, fileInfo.filePath, fileInfo.filePath, file.size, getFileExt(file.name), file.name, file.md5 || '');
                                parentObj.prototype.fileNum = 1;
                            } else {
                                addImage(parentObj, fileInfo.filePath, fileInfo.filePath, file.size, getFileExt(file.name), file.name, file.md5 || '');
                                parentObj.prototype.fileNum++;
                            }

                            uploader.removeFile(file); //从队列中移除

                            //反馈消息
                            if (p.callback != null) {
                                p.callback({
                                    status: '1',
                                    path: fileInfo.filePath,
                                    thumb: fileInfo.filePath,
                                    size: file.size,
                                    ext: getFileExt(file.name),
                                    name: file.name,
                                    md5: file.md5 || '',
                                    fileId: fileInfo.fileId
                                });
                            }
                        } else {
                            // 分片上传中
                            var uploadedChunks = data.data ? data.data.uploadedChunks || 0 : 0;
                            var totalChunks = data.data ? data.data.totalChunks || 1 : 1;
                            progressObj.children(".txt").html("上传中：" + uploadedChunks + "/" + totalChunks);
                        }
                    } else {
                        // 上传失败
                        progressObj.children(".txt").html("上传失败：" + (data.message || "未知错误"));
                        uploader.removeFile(file);
                    }
                } else {
                    // 传统上传响应处理
                    if (data.status == '0') {
                        progressObj.children(".txt").html(data.msg);
                    }
                    if (data.status == '1') {
                        //如果是单文件上传，则清空之前图片列表
                        if (!p.multiple) {
                            parentObj.siblings(".photo-list").children("ul").children("li").remove();
                            addImage(parentObj, data.path, data.thumb, data.size, data.ext, data.name, data.md5);
                            parentObj.prototype.fileNum = 1;
                        } else {
                            addImage(parentObj, data.path, data.thumb, data.size, data.ext, data.name, data.md5);
                            parentObj.prototype.fileNum++;
                        }
                        progressObj.children(".txt").html("上传成功：" + file.name);
                    }
                    uploader.removeFile(file); //从队列中移除

                    //反馈消息
                    if (p.callback != null) {
                        p.callback(data);
                    }
                }
            });

            //不管成功或者失败，文件上传完成时触发
            uploader.on('uploadComplete', function (file) {
                var progressObj = parentObj.children(".upload-progress");
                progressObj.children(".txt").html("上传完成");
                //如果队列为空，则移除进度条
                if (uploader.getStats().queueNum == 0) {
                    progressObj.remove();
                }
            });
        };
        return $(this).each(function () {
            fun($(this));
        });
    }

    // 分片上传处理函数
    function handleChunkUpload(file, uploader, parentObj, options) {
        var progressObj = parentObj.children(".upload-progress");

        // 计算文件MD5
        progressObj.children(".txt").html("正在计算文件MD5...");

        calculateFileMD5(file, uploader, function (md5) {
            file.md5 = md5;

            // 查询上传状态
            progressObj.children(".txt").html("检查文件状态...");

            $.ajax({
                url: options.sendurl.replace('action=upload', 'action=status'),
                type: 'GET',
                data: { fileMD5: md5 },
                dataType: 'json',
                success: function (result) {
                    if (result.success && result.data) {
                        if (result.data.completed) {
                            // 文件已存在，秒传
                            progressObj.children(".txt").html("文件已存在，秒传成功");

                            // 模拟上传成功回调
                            var mockData = {
                                success: true,
                                data: {
                                    completed: true,
                                    filePath: result.data.filePath,
                                    fileId: result.data.fileId
                                }
                            };
                            uploader.trigger('uploadSuccess', file, mockData);
                            return;
                        }
                    }

                    // 设置上传参数
                    uploader.options.formData.fileMD5 = md5;
                    uploader.options.formData.fileName = file.name;
                    uploader.options.formData.fileSize = file.size;
                    uploader.options.formData.directoryId = options.directoryId || 1;

                    // 开始上传
                    progressObj.children(".txt").html("开始上传文件...");
                    uploader.upload(file);
                },
                error: function () {
                    progressObj.children(".txt").html("状态查询失败，直接上传");

                    // 设置上传参数
                    uploader.options.formData.fileMD5 = md5;
                    uploader.options.formData.fileName = file.name;
                    uploader.options.formData.fileSize = file.size;
                    uploader.options.formData.directoryId = options.directoryId || 1;

                    // 开始上传
                    uploader.upload(file);
                }
            });
        });
    }

    // 计算文件MD5 - 使用WebUploader自带的md5File方法
    function calculateFileMD5(file, uploader, callback) {
        try {
            // 使用WebUploader的md5File方法计算MD5
            uploader.md5File(file, 0, file.size)
                .then(function (md5) {
                    callback(md5);
                })
                .catch(function (error) {
                    console.error('MD5计算失败:', error);
                    callback('');
                });
        } catch (error) {
            console.error('MD5计算异常:', error);
            callback('');
        }
    }

    // 获取文件扩展名
    function getFileExt(fileName) {
        var ext = fileName.split('.').pop();
        return ext ? ext.toLowerCase() : '';
    }
});

/*图片相册处理事件
=====================================================*/
//添加图片相册
function addImage(targetObj, originalSrc, thumbSrc, size, ext, data, md5) {
    //插入到相册UL里面
    var newLi = $('<li>'
        + '<input type="hidden" name="hid_photo_name" value="' + size + '|' + originalSrc + '|' + ext + '|' + data + '|' + md5 + '" />'
        //+ '<input type="hidden" name="hid_photo_remark" value="" />'
        + '<div class="img-box" onclick="setFocusImg(this);">'
        + '<img src="' + thumbSrc + '" bigsrc="' + originalSrc + '" layer-src="' + originalSrc + '" />'
        //+ '<span class="remark"><i>暂无描述...</i></span>'
        + '</div>'
        //+ '<a href="javascript:;" onclick="setRemark(this);">描述</a>'
        + '<a href="javascript:;");">删除</a>'
        + '</li>').children('a').click(function () { delImg(this, targetObj) }).end();
    newLi.appendTo(targetObj.siblings(".photo-list").children("ul"));
}
//添加单独图片相册
function addOneImage(targetObj, originalSrc, thumbSrc, size, ext, data) {
    //插入到相册UL里面
    var newLi = $('<li>'
        + '<input type="hidden" name="hid_photo_name" value="' + size + '|' + originalSrc + '|' + ext + '|' + data + '" />'
        //+ '<input type="hidden" name="hid_photo_remark" value="" />'
        + '<div class="img-box" onclick="setFocusImg(this);">'
        + '<img src="' + thumbSrc + '" bigsrc="' + originalSrc + '" layer-src="' + originalSrc + '"   />'
        //+ '<span class="remark"><i>暂无描述...</i></span>'
        + '</div>'
        //+ '<a href="javascript:;" onclick="setRemark(this);">描述</a>'
        //+ '<a href="javascript:;" onclick="delImg(this);">删除</a>'
        + '</li>');
    newLi.appendTo(targetObj.siblings(".photo-list").children("ul"));
    targetObj.hide();
}
//设置相册封面
function setFocusImg(obj) {
    // window.open($(obj).find("img").attr("bigsrc"))
    layer.photos({
        photos: { "data": [{ "src": $(obj).find("img").attr("bigsrc"), }] }
    });
}
//设置图片描述
function setRemark(obj) {
    var parentObj = $(obj); //父对象
    var hidRemarkObj = parentObj.prevAll("input[name='hid_photo_remark']").eq(0); //取得隐藏值
    var d = parent.dialog({
        title: "图片描述",
        content: '<textarea id="ImageRemark" style="margin:10px 0;font-size:12px;padding:3px;color:#000;border:1px #d2d2d2 solid;vertical-align:middle;width:300px;height:50px;">' + hidRemarkObj.val() + '</textarea>',
        button: [{
            value: '批量描述',
            callback: function () {
                var remarkObj = $('#ImageRemark', parent.document);
                if (remarkObj.val() == "") {
                    parent.dialog({
                        title: '提示',
                        content: '亲，总该写点什么吧？',
                        okValue: '确定',
                        ok: function () { },
                        onclose: function () {
                            remarkObj.focus();
                        }
                    }).showModal();
                    return false;
                }
                parentObj.parent().parent().find("li input[name='hid_photo_remark']").val(remarkObj.val());
                parentObj.parent().parent().find("li .img-box .remark i").html(remarkObj.val());
            }
        }, {
            value: '单张描述',
            callback: function () {
                var remarkObj = $('#ImageRemark', parent.document);
                if (remarkObj.val() == "") {
                    parent.dialog({
                        title: '提示',
                        content: '亲，总该写点什么吧？',
                        okValue: '确定',
                        ok: function () { },
                        onclose: function () {
                            remarkObj.focus();
                        }
                    }).showModal();
                    return false;
                }
                hidRemarkObj.val(remarkObj.val());
                parentObj.siblings(".img-box").children(".remark").children("i").html(remarkObj.val());
            },
            autofocus: true
        }]
    }).showModal();
}
//删除图片LI节点
function delImg(obj, targetObj) {
    $(obj).parent().remove(); //删除的LI节点
    targetObj.prototype.fileNum--;
}