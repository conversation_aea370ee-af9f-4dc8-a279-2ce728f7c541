<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WebUploader MD5计算测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .progress {
            margin: 10px 0;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebUploader MD5计算测试</h1>
        <p>此页面用于测试WebUploader自带的md5File方法，无需依赖外部spark-md5库。</p>
        
        <div class="test-section">
            <h3>文件选择</h3>
            <input type="file" id="fileInput" />
            <button class="btn" onclick="calculateMD5()">计算MD5</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="test-section">
            <h3>计算进度</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="progressText">等待选择文件...</div>
        </div>
        
        <div class="test-section">
            <h3>文件信息</h3>
            <div id="fileInfo" class="result">请选择文件</div>
        </div>
        
        <div class="test-section">
            <h3>MD5结果</h3>
            <div id="md5Result" class="result">等待计算...</div>
        </div>
        
        <div class="test-section">
            <h3>性能测试</h3>
            <div id="performanceResult" class="result">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div id="logArea" class="result" style="height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- 只需要WebUploader，不需要spark-md5 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="js/webuploader.min.js"></script>

    <script>
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearResults() {
            document.getElementById('fileInfo').innerHTML = '请选择文件';
            document.getElementById('md5Result').innerHTML = '等待计算...';
            document.getElementById('performanceResult').innerHTML = '等待测试...';
            document.getElementById('logArea').innerHTML = '';
            document.getElementById('progressText').innerHTML = '等待选择文件...';
            document.getElementById('progressBar').style.width = '0%';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').innerHTML = text;
        }

        function calculateMD5() {
            const fileInput = document.getElementById('fileInput');
            
            if (!fileInput.files.length) {
                alert('请先选择文件');
                return;
            }

            const file = fileInput.files[0];
            
            // 显示文件信息
            const fileInfo = `
                文件名: ${file.name}
                文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB
                文件类型: ${file.type || '未知'}
                最后修改: ${new Date(file.lastModified).toLocaleString()}
            `;
            document.getElementById('fileInfo').innerHTML = fileInfo;
            
            log(`开始计算文件MD5: ${file.name}`);
            log(`文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
            
            updateProgress(0, '正在计算MD5...');
            
            const startTime = performance.now();
            
            try {
                // 使用WebUploader自带的md5File方法
                WebUploader.md5File(file, 0, file.size)
                    .progress(function(percentage) {
                        // 进度回调
                        const percent = Math.round(percentage * 100);
                        updateProgress(percent, `计算进度: ${percent}%`);
                        log(`MD5计算进度: ${percent}%`);
                    })
                    .then(function(md5) {
                        // 计算完成
                        const endTime = performance.now();
                        const duration = ((endTime - startTime) / 1000).toFixed(2);
                        
                        updateProgress(100, '计算完成');
                        
                        // 显示MD5结果
                        const md5Result = `
                            <div class="success">
                                <strong>MD5值:</strong> ${md5}
                            </div>
                        `;
                        document.getElementById('md5Result').innerHTML = md5Result;
                        
                        // 显示性能信息
                        const speed = (file.size / 1024 / 1024 / duration).toFixed(2);
                        const performanceResult = `
                            <div class="success">
                                <strong>计算耗时:</strong> ${duration} 秒<br>
                                <strong>计算速度:</strong> ${speed} MB/s<br>
                                <strong>使用方法:</strong> WebUploader.md5File()
                            </div>
                        `;
                        document.getElementById('performanceResult').innerHTML = performanceResult;
                        
                        log(`MD5计算完成: ${md5}`);
                        log(`计算耗时: ${duration} 秒`);
                        log(`计算速度: ${speed} MB/s`);
                    })
                    .catch(function(error) {
                        // 计算失败
                        const endTime = performance.now();
                        const duration = ((endTime - startTime) / 1000).toFixed(2);
                        
                        updateProgress(0, '计算失败');
                        
                        const errorResult = `
                            <div class="error">
                                <strong>错误:</strong> ${error.message || error}
                            </div>
                        `;
                        document.getElementById('md5Result').innerHTML = errorResult;
                        
                        const performanceResult = `
                            <div class="error">
                                <strong>失败耗时:</strong> ${duration} 秒<br>
                                <strong>错误信息:</strong> ${error.message || error}
                            </div>
                        `;
                        document.getElementById('performanceResult').innerHTML = performanceResult;
                        
                        log(`MD5计算失败: ${error.message || error}`);
                        log(`失败耗时: ${duration} 秒`);
                    });
                    
            } catch (error) {
                updateProgress(0, '计算异常');
                
                const errorResult = `
                    <div class="error">
                        <strong>异常:</strong> ${error.message || error}
                    </div>
                `;
                document.getElementById('md5Result').innerHTML = errorResult;
                
                log(`MD5计算异常: ${error.message || error}`);
            }
        }

        // 文件选择变化时自动显示文件信息
        document.getElementById('fileInput').addEventListener('change', function() {
            if (this.files.length > 0) {
                const file = this.files[0];
                log(`选择文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });

        // 页面加载完成
        $(document).ready(function() {
            log('页面加载完成');
            log('WebUploader版本: ' + (WebUploader.version || '未知'));
            log('支持MD5计算: ' + (typeof WebUploader.md5File === 'function' ? '是' : '否'));
        });
    </script>
</body>
</html>
