<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WebUploader File Helper 分片上传演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .config-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-section label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }
        .config-section input, .config-section select {
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        /* 上传组件样式 */
        .upload-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            margin-bottom: 15px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        
        .upload-list {
            border: 1px solid #ddd;
            border-radius: 5px;
            min-height: 100px;
            padding: 10px;
        }
        
        .upload-list__item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 3px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
        }
        
        .upload-list__item.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .upload-list__item.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        
        .upload-list__item-leave {
            opacity: 0;
            transform: translateX(-100%);
        }
        
        .upload-list__item-info {
            flex: 1;
            margin-right: 10px;
        }
        
        .upload-list__title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .upload-list__progress {
            margin-top: 5px;
        }
        
        .upload-list__progress__outer {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .upload-list__progress__inner {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .upload-list__progress__text {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .upload-list__item-status {
            margin-right: 10px;
        }
        
        .upload-list__item-status img {
            width: 20px;
            height: 20px;
        }
        
        .upload-list__item-close {
            cursor: pointer;
            padding: 5px;
        }
        
        .upload-list__item-close img {
            width: 16px;
            height: 16px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        
        .log-area {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebUploader File Helper 分片上传演示</h1>
        <p>此页面演示基于类的WebUploader文件上传组件，支持分片上传和断点续传功能。</p>
        
        <div class="config-section">
            <h3>上传配置</h3>
            <div>
                <label>目录ID:</label>
                <input type="number" id="directoryId" value="1" />
            </div>
            <div>
                <label>分片大小(MB):</label>
                <select id="chunkSize">
                    <option value="1">1MB</option>
                    <option value="2" selected>2MB</option>
                    <option value="5">5MB</option>
                </select>
            </div>
            <div>
                <label>文件类型:</label>
                <input type="text" id="fileTypes" value="*" style="width: 300px;" />
            </div>
            <div>
                <label>文件大小限制(MB):</label>
                <input type="number" id="fileSize" value="1000" />
            </div>
            <div>
                <label>启用分片上传:</label>
                <input type="checkbox" id="enableChunk" checked />
            </div>
            <div>
                <label>多文件上传:</label>
                <input type="checkbox" id="multipleFiles" />
            </div>
            <div>
                <label>文件数量限制:</label>
                <input type="number" id="fileNumLimit" value="5" />
            </div>
        </div>

        <!-- 单文件上传示例 -->
        <div class="demo-section">
            <h3>单文件分片上传</h3>
            <div id="singleUpload"></div>
        </div>

        <!-- 多文件上传示例 -->
        <div class="demo-section">
            <h3>多文件分片上传</h3>
            <div id="multiUpload"></div>
        </div>

        <!-- 控制按钮 -->
        <div class="demo-section">
            <h3>控制操作</h3>
            <button class="btn btn-primary" onclick="initUploaders()">重新初始化</button>
            <button class="btn btn-warning" onclick="refreshFileList()">刷新文件列表</button>
            <button class="btn btn-success" onclick="getUploadedFiles()">获取已上传文件</button>
        </div>

        <!-- 日志区域 -->
        <div class="demo-section">
            <h3>操作日志</h3>
            <div class="log-area" id="logArea"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="js/webuploader.min.js"></script>
    <script src="js/webuploader.helper.file.js"></script>

    <script>
        let singleUploader = null;
        let multiUploader = null;

        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        function getConfig() {
            return {
                directoryId: parseInt($('#directoryId').val()),
                chunkSize: parseInt($('#chunkSize').val()),
                fileTypes: $('#fileTypes').val(),
                fileSize: parseInt($('#fileSize').val()) * 1024 * 1024, // 转换为字节
                enableChunk: $('#enableChunk').is(':checked'),
                multipleFiles: $('#multipleFiles').is(':checked'),
                fileNumLimit: parseInt($('#fileNumLimit').val())
            };
        }

        function initUploaders() {
            const config = getConfig();
            
            log("开始初始化上传器...");
            log(`配置: 目录ID=${config.directoryId}, 分片大小=${config.chunkSize}MB, 启用分片=${config.enableChunk}`);

            // 清空现有上传器
            $('#singleUpload').empty();
            $('#multiUpload').empty();

            // 单文件上传器
            $('#singleUpload').initUploader({
                server: 'ajax/uploader.ashx',
                swf: 'js/Uploader.swf',
                accept: config.fileTypes,
                fileSizeLimit: config.fileSize,
                multiple: false,
                chunked: config.enableChunk,
                chunkSize: config.chunkSize,
                directoryId: config.directoryId,
                fileNumLimit: 1,
                btnText: "选择单个文件",
                onSuccess: function(data) {
                    log("单文件上传成功: " + JSON.stringify(data));
                },
                onRemove: function(data) {
                    log("单文件移除: " + JSON.stringify(data));
                }
            });

            // 多文件上传器
            $('#multiUpload').initUploader({
                server: 'ajax/uploader.ashx',
                swf: 'js/Uploader.swf',
                accept: config.fileTypes,
                fileSizeLimit: config.fileSize,
                multiple: config.multipleFiles,
                chunked: config.enableChunk,
                chunkSize: config.chunkSize,
                directoryId: config.directoryId,
                fileNumLimit: config.fileNumLimit,
                btnText: "选择多个文件",
                onSuccess: function(data) {
                    log("多文件上传成功: " + JSON.stringify(data));
                },
                onRemove: function(data) {
                    log("多文件移除: " + JSON.stringify(data));
                }
            });

            // 保存上传器实例
            singleUploader = $('#singleUpload').data('uploader');
            multiUploader = $('#multiUpload').data('uploader');

            log("上传器初始化完成");
        }

        function refreshFileList() {
            // 模拟刷新文件列表
            const mockFileList = [
                { name: "示例文件1.pdf", path: "/dms/doc/1001/example1.pdf" },
                { name: "示例文件2.docx", path: "/dms/doc/1001/example2.docx" }
            ];

            if (singleUploader) {
                singleUploader.refreshFileList(mockFileList.slice(0, 1));
                log("单文件上传器文件列表已刷新");
            }

            if (multiUploader) {
                multiUploader.refreshFileList(mockFileList);
                log("多文件上传器文件列表已刷新");
            }
        }

        function getUploadedFiles() {
            log("获取已上传文件列表...");
            
            if (singleUploader) {
                log("单文件上传器文件数量: " + singleUploader.fileNum);
            }
            
            if (multiUploader) {
                log("多文件上传器文件数量: " + multiUploader.fileNum);
            }
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            initUploaders();
            
            // 配置变更时重新初始化
            $('#directoryId, #chunkSize, #fileTypes, #fileSize, #enableChunk, #multipleFiles, #fileNumLimit').change(function() {
                log("配置已更改，将在下次初始化时生效");
            });
            
            log("页面加载完成");
            log("WebUploader版本: " + (WebUploader.version || '未知'));
        });
    </script>
</body>
</html>
