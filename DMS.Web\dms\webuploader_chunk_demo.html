<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WebUploader分片上传演示</title>
    <link rel="stylesheet" type="text/css" href="css/webuploader.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .upload-progress {
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f8ff;
            border: 1px solid #b0d4f1;
            border-radius: 3px;
            position: relative;
        }
        .upload-progress .txt {
            display: block;
            margin-bottom: 5px;
            color: #333;
        }
        .upload-progress .bar {
            display: block;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        .upload-progress .bar b {
            display: block;
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s;
        }
        .upload-progress .close {
            position: absolute;
            top: 5px;
            right: 10px;
            color: #999;
            text-decoration: none;
            font-weight: bold;
        }
        .upload-progress .close:hover {
            color: #333;
        }
        .photo-list {
            margin: 20px 0;
        }
        .photo-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .photo-list li {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
            vertical-align: top;
            width: 200px;
        }
        .photo-list li img {
            max-width: 100%;
            height: auto;
            display: block;
            margin-bottom: 5px;
        }
        .photo-list li .file-info {
            font-size: 12px;
            color: #666;
        }
        .config-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .config-section label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }
        .config-section input, .config-section select {
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .log-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .log-area {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebUploader分片上传演示</h1>
        
        <div class="config-section">
            <h3>上传配置</h3>
            <div>
                <label>目录ID:</label>
                <input type="number" id="directoryId" value="1" />
            </div>
            <div>
                <label>分片大小(MB):</label>
                <select id="chunkSize">
                    <option value="1">1MB</option>
                    <option value="2" selected>2MB</option>
                    <option value="5">5MB</option>
                    <option value="10">10MB</option>
                </select>
            </div>
            <div>
                <label>文件类型:</label>
                <input type="text" id="fileTypes" value="jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,txt" style="width: 300px;" />
            </div>
            <div>
                <label>文件大小限制(KB):</label>
                <input type="number" id="fileSize" value="102400" />
            </div>
            <div>
                <label>启用分片上传:</label>
                <input type="checkbox" id="enableChunk" checked />
            </div>
            <div>
                <label>多文件上传:</label>
                <input type="checkbox" id="multipleFiles" />
            </div>
        </div>

        <!-- 单文件上传示例 -->
        <div class="upload-section">
            <h3>单文件上传</h3>
            <div id="singleUpload">
                <input type="hidden" class="upload-path" value="" />
                <a href="javascript:void(0);" class="upload-btn btn btn-primary">选择文件</a>
                <div class="photo-list">
                    <ul></ul>
                </div>
            </div>
        </div>

        <!-- 多文件上传示例 -->
        <div class="upload-section">
            <h3>多文件上传</h3>
            <div id="multiUpload">
                <input type="hidden" class="upload-path" value="" />
                <a href="javascript:void(0);" class="upload-btn btn btn-success">选择多个文件</a>
                <div class="photo-list">
                    <ul></ul>
                </div>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="log-section">
            <h3>上传日志</h3>
            <div class="log-area" id="logArea"></div>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="js/webuploader.min.js"></script>
    <script src="js/webuploader.helper.js"></script>

    <script>
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        function initUploader() {
            const directoryId = parseInt($('#directoryId').val());
            const chunkSize = parseInt($('#chunkSize').val());
            const fileTypes = $('#fileTypes').val();
            const fileSize = parseInt($('#fileSize').val());
            const enableChunk = $('#enableChunk').is(':checked');
            const multipleFiles = $('#multipleFiles').is(':checked');

            // 单文件上传配置
            $("#singleUpload").InitUploader({
                sendurl: "ajax/uploader.ashx",
                swf: "js/Uploader.swf",
                filetypes: fileTypes,
                filesize: fileSize,
                multiple: false,
                chunked: enableChunk,
                chunkSize: chunkSize,
                directoryId: directoryId,
                callback: function(data) {
                    log("单文件上传回调: " + JSON.stringify(data));
                }
            });

            // 多文件上传配置
            $("#multiUpload").InitUploader({
                sendurl: "ajax/uploader.ashx",
                swf: "js/Uploader.swf",
                filetypes: fileTypes,
                filesize: fileSize,
                multiple: multipleFiles,
                chunked: enableChunk,
                chunkSize: chunkSize,
                directoryId: directoryId,
                fileNumLimit: 5,
                callback: function(data) {
                    log("多文件上传回调: " + JSON.stringify(data));
                }
            });

            log("上传器初始化完成");
            log("配置: 目录ID=" + directoryId + ", 分片大小=" + chunkSize + "MB, 启用分片=" + enableChunk);
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            initUploader();
            
            // 配置变更时重新初始化
            $('#directoryId, #chunkSize, #fileTypes, #fileSize, #enableChunk, #multipleFiles').change(function() {
                log("配置已更改，重新初始化上传器...");
                initUploader();
            });
            
            log("页面加载完成");
        });
    </script>
</body>
</html>
