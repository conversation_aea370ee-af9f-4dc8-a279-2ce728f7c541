﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.IO;
using System.Data;
using System.Web.SessionState;

namespace DMS.Web.dms.ajax
{
    /// <summary>
    /// dms_file_manage 的摘要说明
    /// </summary>
    public class dms_file_manage : I<PERSON>ttp<PERSON><PERSON><PERSON>, IRequiresSessionState
    {
        YunEdu.Authority.AdminCommonJC _ac = new YunEdu.Authority.AdminCommonJC();
        BLL.dms_directory bll_dms_directory = new BLL.dms_directory();
        BLL.dms_files bll_dms_files = new BLL.dms_files();
        BLL.dms_auth bll_dms_auth = new BLL.dms_auth();
        YunEdu.BLL.JC_TeacherDept bll_teacherDept = new YunEdu.BLL.JC_TeacherDept();
        DMS.BLL.dms_config bll_dms_config = new DMS.BLL.dms_config();

        public void ProcessRequest(HttpContext context)
        {
            if (!_ac.User.Identity.IsAuthenticated)
            {
                return;
            }
            string action = context.Request.QueryString["action"];
            if (string.IsNullOrEmpty(action))
            {
                action = context.Request.Form["action"];
            }

            switch (action)
            {
                case "getDirectoryAndFiles": // 获取目录和文件
                    GetDirectoryAndFiles(context);
                    break;
                case "delDirectoryOrFiles": // 删除目录或文件
                    DeleteDirectoryOrFiles(context);
                    break;
                case "addDirectory": // 添加目录
                    AddDirectory(context);
                    break;
                case "updateFileName": // 更新文件名称
                    UpdateFileName(context);
                    break;
                case "updateDirectoryName": // 更新目录名称
                    UpdateDirectoryName(context);
                    break;
                case "searchFiles": // 查询
                    SearchFiles(context);
                    break;
                case "setFileStatus": // 设置文件公开或不公开
                    SetFileStatus(context);
                    break;
                case "testFtpConnection": // 测试FTP连接
                    TestFtpConnection(context);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 添加目录
        /// </summary>
        /// <param name="context"></param>
        private void AddDirectory(HttpContext context)
        {
            string result = string.Empty;
            string name = context.Request.Form["name"];
            string parentId = context.Request.Form["parentId"];
            // 判断参数是否正常，并且是否是校级管理员
            if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(parentId) && _ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                try
                {
                    Model.dms_directory model_parent = null;
                    // 判断是否为0
                    if (parentId.Equals("0"))
                    {
                        model_parent = bll_dms_directory.GetRoot(_ac.modelAreaUser.ColumnID);
                    }
                    else
                    {
                        model_parent = bll_dms_directory.GetModel(int.Parse(parentId));
                    }

                    if (model_parent == null)
                    {
                        result = "3"; // 父目录不存在
                        context.Response.Write(result);
                        return;
                    }

                    // 检查目录名是否已存在
                    string strDirectoryName = YunEdu.Common.StringHelper.ToText(name);
                    if (CheckDirectoryExists(model_parent.Id, strDirectoryName))
                    {
                        result = "4"; // 目录名已存在
                        context.Response.Write(result);
                        return;
                    }

                    // 创建目录模型
                    Model.dms_directory model = new Model.dms_directory();
                    model.Name = strDirectoryName;
                    model.ParentId = model_parent.Id;
                    model.ColumnId = model_parent.ColumnId;
                    model.ColumnPath = model_parent.ColumnPath;
                    model.Creator = new Guid(_ac.UserId);
                    model.CreateTime = DateTime.Now;
                    model.DeptId = 0;

                    // 添加到数据库
                    int nDirectoryId = bll_dms_directory.Add(model);

                    if (nDirectoryId > 0)
                    {
                        result = "1"; // 成功
                    }
                    else
                    {
                        result = "3"; // 数据库操作失败
                    }
                }
                catch (Exception)
                {
                    result = "3";
                }
            }
            else
            {
                result = "2";
            }
            context.Response.Write(result);
        }

        /// <summary>
        /// 更新文件名称
        /// </summary>
        /// <param name="context"></param>
        private void UpdateFileName(HttpContext context)
        {
            string result = "2";
            Guid id = Guid.Empty;
            string _id = context.Request.Form["id"];
            string _name = context.Request.Form["name"];
            // 判断参数是否有效
            if (!string.IsNullOrEmpty(_id) && Guid.TryParse(_id, out id) && !string.IsNullOrEmpty(_name))
            {
                Model.dms_files model_dms_files = bll_dms_files.GetModel(id);
                if (model_dms_files != null)
                {
                    bool canEdit = false;
                    // 判断当前用户是否是校级管理员或者是当前文件的创建人
                    if (model_dms_files.Creator.ToString().Equals(_ac.UserId) || _ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                    {
                        canEdit = true;
                    }
                    else
                    {
                        // 获取权限
                        Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), model_dms_files.DirectoryPath);
                        // 判断是否有编辑权限
                        if (model_user_auth != null && model_user_auth.Auth.IndexOf("2") > -1)
                        {
                            canEdit = true;
                        }
                    }
                    // 判断是否可以进行编辑
                    if (canEdit)
                    {
                        string strNewFileName = YunEdu.Common.StringHelper.ToText(_name);

                        // 如果文件名没有改变，直接返回成功
                        if (model_dms_files.FileName.Equals(strNewFileName))
                        {
                            result = "1";
                            context.Response.Write(result);
                            return;
                        }

                        // 获取目录信息
                        Model.dms_directory directory = bll_dms_directory.GetModel(model_dms_files.DirectoryId);
                        if (directory != null)
                        {
                            // 检查新文件名是否已存在
                            if (CheckFileExists(model_dms_files.DirectoryId, strNewFileName))
                            {
                                result = "4"; // 文件名已存在
                                context.Response.Write(result);
                                return;
                            }

                            // 更新数据库
                            model_dms_files.FileName = strNewFileName;
                            bll_dms_files.Update(model_dms_files);
                            result = "1";
                        }
                        else
                        {
                            result = "3"; // 目录不存在
                        }
                    }
                }
            }
            context.Response.Write(result);
        }

        /// <summary>
        /// 更新文件名称
        /// </summary>
        /// <param name="context"></param>
        private void SetFileStatus(HttpContext context)
        {
            string result = "2";
            Guid id = Guid.Empty;
            string _id = context.Request.Form["id"];
            string _status = context.Request.Form["status"];
            // 判断参数是否有效
            if (!string.IsNullOrEmpty(_id) && Guid.TryParse(_id, out id) && !string.IsNullOrEmpty(_status))
            {
                Model.dms_files model_dms_files = bll_dms_files.GetModel(id);
                if (model_dms_files != null)
                {
                    bool canEdit = false;
                    // 判断当前用户是否是校级管理员或者是当前文件的创建人
                    if (model_dms_files.Creator.Equals(new Guid(_ac.UserId)) || _ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                    {
                        canEdit = true;
                    }
                    else
                    {
                        // 获取权限
                        Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), model_dms_files.DirectoryPath);
                        if (model_user_auth != null && model_user_auth.Auth.IndexOf("2") > -1)
                        {
                            canEdit = true;
                        }
                    }
                    if (canEdit)
                    {
                        model_dms_files.IsOpen = int.Parse(_status);
                        bll_dms_files.Update(model_dms_files);
                        result = "1";
                    }
                }
            }
            context.Response.Write(result);
        }

        /// <summary>
        /// 更新目录名称
        /// </summary>
        /// <param name="context"></param>
        private void UpdateDirectoryName(HttpContext context)
        {
            string result = "2";
            int id = 0;
            string _id = context.Request.Form["id"];
            string _name = context.Request.Form["name"];
            if (!string.IsNullOrEmpty(_id) && int.TryParse(_id, out id) && !string.IsNullOrEmpty(_name))
            {
                Model.dms_directory model_dms_directory = bll_dms_directory.GetModel(id);
                if (model_dms_directory != null)
                {
                    bool canEdit = false;
                    // 判断当前用户是否是校级管理员
                    if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                    {
                        canEdit = true;
                    }
                    else
                    {
                        // 获取权限
                        Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), model_dms_directory.Path);
                        if (model_user_auth != null && model_user_auth.Auth.IndexOf("2") > -1)
                        {
                            canEdit = true;
                        }
                    }
                    if (canEdit)
                    {
                        string strNewDirectoryName = YunEdu.Common.StringHelper.ToText(_name);

                        // 如果目录名没有改变，直接返回成功
                        if (model_dms_directory.Name.Equals(strNewDirectoryName))
                        {
                            result = "1";
                            context.Response.Write(result);
                            return;
                        }

                        // 检查新目录名是否已存在
                        if (CheckDirectoryExists(model_dms_directory.ParentId, strNewDirectoryName))
                        {
                            result = "4"; // 目录名已存在
                            context.Response.Write(result);
                            return;
                        }

                        // 更新数据库
                        model_dms_directory.Name = strNewDirectoryName;
                        bll_dms_directory.Update(model_dms_directory);
                        result = "1";
                    }
                }
            }
            context.Response.Write(result);
        }

        /// <summary>
        /// 删除目录或文件
        /// </summary>
        /// <param name="context"></param>
        private void DeleteDirectoryOrFiles(HttpContext context)
        {
            string directoryIds = context.Request.QueryString["directorys"];
            string fileIds = context.Request.QueryString["files"];

            #region 删除目录
            // 判断是否有要删除的目录
            if (!string.IsNullOrEmpty(directoryIds))
            {
                foreach (var item in directoryIds.Split("|".ToArray(), StringSplitOptions.RemoveEmptyEntries))
                {
                    try
                    {
                        Model.dms_directory model_dms_directory = bll_dms_directory.GetModel(int.Parse(item));
                        if (model_dms_directory != null)
                        {
                            bool canDel = false;
                            // 判断是否为校级管理员
                            if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                            {
                                canDel = true;
                            }
                            else
                            {
                                // 获取权限
                                Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), model_dms_directory.Path);
                                // 判断权限
                                if (model_user_auth != null && model_user_auth.Auth.IndexOf("3") > -1)
                                {
                                    canDel = true;
                                }
                            }

                            if (canDel)
                            {
                                // 获取目录下所有文件
                                DataSet dsFiles = bll_dms_files.GetList(model_dms_directory.Id);
                                if (dsFiles != null && dsFiles.Tables[0].Rows.Count > 0)
                                {
                                    foreach (DataRow file in dsFiles.Tables[0].Rows)
                                    {
                                        string filePath = file["FilePath"].ToString();
                                        if (!string.IsNullOrEmpty(filePath))
                                        {
                                            filePath = context.Server.MapPath(filePath);
                                            if (File.Exists(filePath))
                                            {
                                                File.Delete(filePath);
                                            }
                                        }
                                    }
                                }
                                // 删除目录本身
                                bll_dms_directory.Delete(model_dms_directory.Id);
                            }
                        }
                    }
                    catch (Exception) // 异常处理
                    {
                    }
                }
            }
            #endregion

            #region 删除文件

            // 判断是否有要删除的文件
            if (!string.IsNullOrEmpty(fileIds))
            {
                foreach (var item in fileIds.Split("|".ToArray(), StringSplitOptions.RemoveEmptyEntries))
                {
                    try
                    {
                        Model.dms_files model_files = bll_dms_files.GetModel(new Guid(item));
                        // 判断是否有数据
                        if (model_files != null)
                        {
                            bool canDel = false;
                            if (model_files.Creator.ToString().Equals(_ac.UserId) || _ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                            {
                                canDel = true;
                            }
                            else
                            {
                                // 获取权限
                                Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), model_files.DirectoryPath);
                                if (model_user_auth != null && model_user_auth.Auth.IndexOf("3") > -1)
                                {
                                    canDel = true;
                                }
                            }
                            // 判断是否拥有删除权限
                            if (canDel)
                            {
                                // 删除
                                bll_dms_files.Delete(model_files.Id);
                                // 判断是否有路径
                                if (!string.IsNullOrEmpty(model_files.FilePath))
                                {
                                    string filePath = context.Server.MapPath(model_files.FilePath);
                                    if (File.Exists(filePath))
                                    {
                                        File.Delete(filePath);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception)
                    {
                    }
                }
            }

            #endregion

            var result = new
            {
                code = true,
                msg = "删除成功！"
            };
            context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(result));
        }

        /// <summary>
        /// 转换方法
        /// </summary>
        /// <param name="size">字节值</param>
        /// <returns></returns>
        private String HumanReadableFilesize(double size)
        {
            String[] units = new String[] { "KB", "MB", "GB", "TB", "PB" };
            double mod = 1024.0;
            int i = 0;
            while (size >= mod)
            {
                size /= mod;
                i++;
            }
            return Math.Round(size, 2) + units[i];

        }

        /// <summary>
        /// 获取文件夹和文件列表
        /// </summary>
        /// <param name="context"></param>
        private void GetDirectoryAndFiles(HttpContext context)
        {
            int parentId = 0;
            if (context.Request.QueryString["parentId"] != null)
                int.TryParse(context.Request.QueryString["parentId"], out parentId);

            string menuId = context.Request.QueryString["menuId"];

            Model.dms_directory model_parent = null;
            // 判断父级是否为0
            if (parentId == 0)
            {
                // 判断是否初始化过
                Model.dms_directory model_root = bll_dms_directory.GetRoot(_ac.modelAreaUser.ColumnID);
                if (model_root == null)
                {
                    InitDms(_ac.modelAreaUser);
                }
                // 重新获取父级节点信息
                model_parent = bll_dms_directory.GetRoot(_ac.modelAreaUser.ColumnID);
            }
            else
            {
                model_parent = bll_dms_directory.GetModel(parentId);
            }
            List<Model.dms_FileJson> fileList = new List<Model.dms_FileJson>();

            // 判断是否有授权按钮权限
            bool isAuthEnable = false;
            DataSet ds_module = new YunEdu.BLL.aspnet_Modules().GetList(string.Format("ParentModuleId='{0}' and ButtonID='btnAuth' and IsButton=1 ", YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(menuId)));
            if (ds_module != null && ds_module.Tables[0].Rows.Count > 0)
            {
                // 判断是否有校级权限
                string AuthorityStr = string.Empty;
                if (context.Session["AuthorityStr"] != null)
                {
                    AuthorityStr = YunEdu.Common.DEncrypt.DESEncrypt.Decrypt(HttpContext.Current.Session["AuthorityStr"].ToString());//解密的
                }
                if (AuthorityStr.Contains(string.Format("|{0}|", ds_module.Tables[0].Rows[0]["ModuleId"].ToString())))
                {
                    isAuthEnable = true;
                }
            }

            // 获取目录列表
            DataSet dsDirectorys = bll_dms_directory.GetList(model_parent.ColumnId, model_parent.Id);
            // 判断是否有数据
            if (dsDirectorys != null && dsDirectorys.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow item in dsDirectorys.Tables[0].Rows)
                {
                    string authStr = string.Empty;
                    if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                    {
                        authStr = "1|2|3";
                        // 判断是否有授权按钮权限
                        if (isAuthEnable)
                        {
                            authStr += "|4";
                        }
                    }
                    else
                    {
                        Model.dms_auth model_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), item.Field<string>("Path"));
                        if (model_auth != null)
                        {
                            authStr = model_auth.Auth;
                        }
                    }
                    fileList.Add(new Model.dms_FileJson
                    {
                        Id = item["Id"].ToString(),
                        FileName = item["Name"].ToString(),
                        FileType = "",
                        FileSize = "",
                        FilePath = "",
                        IsFile = 0,
                        Auth = authStr,
                        IsOpen = false,
                        CreateTime = item["CreateTime"] == null ? "" : item.Field<DateTime?>("CreateTime").Value.ToString("yyyy-MM-dd HH:mm:ss")
                    });
                }
            }
            // 获取当前用户在当前目录下的权限
            string userAuth = string.Empty;
            // 判断是否有校级权限
            if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                userAuth = "1|2|3";
                // 判断是否有授权按钮权限
                if (isAuthEnable)
                {
                    userAuth += "|4";
                }
            }
            else
            {
                Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), model_parent.Path);
                if (model_user_auth != null)
                {
                    userAuth = model_user_auth.Auth;
                }
            }
            // 获取文件列表
            DataSet dsFiles = null;
            // 判断是否有校级权限或者具有编辑权限
            if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()) || userAuth.IndexOf("2") > -1)
            {
                dsFiles = bll_dms_files.GetList(model_parent.Id);
            }
            else
            {
                dsFiles = bll_dms_files.GetList(model_parent.Id, 1, new Guid(_ac.UserId));
            }
            // 判断是否有数据
            if (dsFiles != null && dsFiles.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow item in dsFiles.Tables[0].Rows)
                {
                    fileList.Add(new Model.dms_FileJson
                    {
                        Id = item["Id"].ToString(),
                        FileName = (item["FileName"].ToString().Length > 50 ? item["FileName"].ToString().Substring(0, 50) + "..." : item["FileName"].ToString()),
                        FileType = item["FileFormat"].ToString(),
                        FileSize = HumanReadableFilesize(double.Parse(item["FileSize"].ToString())),
                        FilePath = "",
                        IsFile = 1,
                        Auth = item["Creator"].ToString().Equals(_ac.UserId) ? "1|2|3" : userAuth,
                        IsOpen = item.Field<int>("IsOpen") == 1,
                        CreateTime = item["CreateTime"] == null ? "" : item.Field<DateTime?>("CreateTime").Value.ToString("yyyy-MM-dd HH:mm:ss")
                    });
                }
            }

            var result = new
            {
                UserAuth = userAuth,
                FileList = fileList,
                RootId = model_parent.Id
            };

            context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(result));
        }

        /// <summary>
        /// 获取文件夹和文件列表
        /// </summary>
        /// <param name="context"></param>
        private void SearchFiles(HttpContext context)
        {
            // 父级id
            string _parentId = context.Request.QueryString["parentId"];
            // 搜索关键字
            string _keyword = YunEdu.Common.StringHelper.ToText(context.Request.QueryString["keyword"]);
            // 开始时间
            DateTime startTime = DateTime.MinValue;
            string _startTime = context.Request.QueryString["start"];
            if (!string.IsNullOrEmpty(_startTime)) DateTime.TryParse(_startTime, out startTime);
            // 结束时间
            DateTime endTime = DateTime.MinValue;
            string _endTime = context.Request.QueryString["end"];
            if (!string.IsNullOrEmpty(_endTime)) DateTime.TryParse(_endTime, out endTime);

            List<Model.dms_FileJson> fileList = new List<Model.dms_FileJson>();

            Model.dms_directory model_parent = null;
            // 判断是否为0
            if (_parentId.Equals("0"))
            {
                model_parent = bll_dms_directory.GetRoot(_ac.modelAreaUser.ColumnID);
            }
            else
            {
                model_parent = bll_dms_directory.GetModel(int.Parse(_parentId));
            }

            // 获取文件列表
            DataSet dsFiles = null;
            // 判断是否有校级权限
            if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                dsFiles = bll_dms_files.GetList(model_parent.Path, _keyword, startTime, endTime);
            }
            else
            {
                dsFiles = bll_dms_files.GetList(model_parent.Path, _keyword, startTime, endTime, 1, new Guid(_ac.UserId));
            }

            // 判断是否有数据
            if (dsFiles != null && dsFiles.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow item in dsFiles.Tables[0].Rows)
                {
                    Model.dms_FileJson model = new Model.dms_FileJson
                    {
                        Id = item["Id"].ToString(),
                        FileName = item["FileName"].ToString(),
                        FileType = item["FileFormat"].ToString(),
                        FileSize = HumanReadableFilesize(double.Parse(item["FileSize"].ToString())),
                        FilePath = "",
                        IsFile = 1,
                        Auth = "",
                        IsOpen = item.Field<int>("IsOpen") == 1,
                        CreateTime = item["CreateTime"] == null ? "" : item.Field<DateTime?>("CreateTime").Value.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                    // 获取权限
                    Model.dms_auth model_user_auth = bll_dms_auth.GetModel(new Guid(_ac.UserId), item["DirectoryPath"].ToString());
                    string userAuth = string.Empty;
                    if (model_user_auth != null)
                    {
                        userAuth = model_user_auth.Auth;
                    }
                    // 判断是否有校级权限
                    if (_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                    {
                        userAuth += "|4";
                    }
                    model.Auth = item["Creator"].ToString().Equals(_ac.UserId) ? "1|2|3" : userAuth;
                    fileList.Add(model);
                }
            }
            context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(fileList));
        }

        /// <summary>
        /// 初始化系统
        /// </summary>
        /// <param name="model">地区信息</param>
        /// <param name="filePath"></param>
        private void InitDms(YunEdu.Model.BM_Areas model)
        {
            // 获取FTP配置
            DMS.Model.dms_config ftpConfig = GetFtpConfigByColumnPath(model.ColumnPath);

            // 创建本地文件夹
            string _relativePath = bll_dms_directory.GetRootDirectory(model.ColumnID);
            string _absolutePath = _ac.Server.MapPath(_relativePath);
            if (!Directory.Exists(_absolutePath))
            {
                Directory.CreateDirectory(_absolutePath);
            }

            // 如果配置了FTP服务器，创建FTP根目录
            if (IsFtpServer(ftpConfig))
            {
                try
                {
                    string strFtpRootPath = GetFtpRootPath(ftpConfig);
                    if (!CreateFtpDirectory(strFtpRootPath, ftpConfig))
                    {
                        // FTP根目录创建失败，记录日志但不影响后续操作
                    }
                }
                catch
                {
                    // FTP操作失败，记录日志但不影响后续操作
                }
            }

            // 添加至数据库
            Model.dms_directory model_directory = new Model.dms_directory();
            model_directory.ColumnId = model.ColumnID;
            model_directory.ColumnPath = model.ColumnPath;
            model_directory.Creator = new Guid(_ac.UserId);
            model_directory.CreateTime = DateTime.Now;
            model_directory.Name = model.ColumnID.ToString();
            model_directory.OrderId = 0;
            model_directory.ParentId = 0;
            model_directory.DeptId = 0;
            int directoryId = bll_dms_directory.Add(model_directory);

            if (directoryId > 0)
            {
                // 重新获取数据
                model_directory = bll_dms_directory.GetModel(directoryId);

                // 添加权限
                Model.dms_auth model_auth = new Model.dms_auth();
                model_auth.ColumnId = model_directory.ColumnId;
                model_auth.ColumnPath = model_directory.ColumnPath;
                model_auth.DirectoryId = model_directory.Id;
                model_auth.DirectoryPath = model_directory.Path;
                model_auth.Auth = "1|2|3";
                model_auth.UserId = model_directory.Creator;
                model_auth.Creator = model_directory.Creator;
                model_auth.CreateTime = DateTime.Now;
                bll_dms_auth.Add(model_auth);

                YunEdu.BLL.JC_Department bll_dept = new YunEdu.BLL.JC_Department();
                // 获取地区下所有部门
                DataTable dtDepts = bll_dept.GetList("ColumnId=" + model.ColumnID).Tables[0];
                // 判断是否有数据
                if (dtDepts != null && dtDepts.Rows.Count > 0)
                {
                    CreateDirectory(model, dtDepts, directoryId, 0, ftpConfig);
                }
            }
        }

        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="model"></param>
        /// <param name="data"></param>
        /// <param name="parentId"></param>
        private void CreateDirectory(YunEdu.Model.BM_Areas model, DataTable data, int parentId, int parentDeptId, Model.dms_config ftpConfig = null)
        {
            DataRow[] depts = data.Select("ParentId=" + parentDeptId);
            if (depts != null && depts.Length > 0)
            {
                int index = 1;
                foreach (DataRow item in depts)
                {
                    string _deptName = item["DeptName"].ToString();
                    int _deptId = int.Parse(item["ID"].ToString());
                    if (!string.IsNullOrEmpty(_deptName))
                    {
                        // 检查目录名是否已存在
                        if (CheckDirectoryExists(parentId, _deptName))
                        {
                            // 目录已存在，跳过创建但继续递归处理子目录
                            // 使用BLL层方法获取已存在的目录ID
                            DataSet dsExisting = bll_dms_directory.GetListByParentIdAndName(parentId, _deptName);
                            if (dsExisting != null && dsExisting.Tables[0].Rows.Count > 0)
                            {
                                int existingDirectoryId = int.Parse(dsExisting.Tables[0].Rows[0]["Id"].ToString());
                                CreateDirectory(model, data, existingDirectoryId, _deptId, ftpConfig);
                            }
                            index++;
                            continue;
                        }

                        // 添加至数据库
                        Model.dms_directory model_directory = new Model.dms_directory();
                        model_directory.ColumnId = model.ColumnID;
                        model_directory.ColumnPath = model.ColumnPath;
                        model_directory.Creator = new Guid(_ac.UserId);
                        model_directory.CreateTime = DateTime.Now;
                        model_directory.Name = _deptName;
                        model_directory.OrderId = index;
                        model_directory.ParentId = parentId;
                        model_directory.DeptId = _deptId;
                        int directoryId = bll_dms_directory.Add(model_directory);

                        if (directoryId > 0)
                        {
                            // 重新获取数据
                            model_directory = bll_dms_directory.GetModel(directoryId);

                            // 添加权限
                            Model.dms_auth model_auth = new Model.dms_auth();
                            model_auth.ColumnId = model_directory.ColumnId;
                            model_auth.ColumnPath = model_directory.ColumnPath;
                            model_auth.DirectoryId = model_directory.Id;
                            model_auth.DirectoryPath = model_directory.Path;
                            model_auth.Auth = "1|2|3";
                            model_auth.UserId = model_directory.Creator;
                            model_auth.Creator = model_directory.Creator;
                            model_auth.CreateTime = DateTime.Now;
                            bll_dms_auth.Add(model_auth);

                            // 获取部门下的人
                            DataSet dsTeachers = bll_teacherDept.GetListByDeptId(_deptId.ToString());
                            if (dsTeachers != null && dsTeachers.Tables[0].Rows.Count > 0)
                            {
                                foreach (DataRow teacher in dsTeachers.Tables[0].Rows)
                                {
                                    // 添加权限
                                    Model.dms_auth model_teacher_auth = new Model.dms_auth();
                                    model_teacher_auth.ColumnId = model_directory.ColumnId;
                                    model_teacher_auth.ColumnPath = model_directory.ColumnPath;
                                    model_teacher_auth.DirectoryId = model_directory.Id;
                                    model_teacher_auth.DirectoryPath = model_directory.Path;
                                    model_teacher_auth.Auth = "1";
                                    model_teacher_auth.UserId = new Guid(teacher["UserID"].ToString());
                                    model_teacher_auth.Creator = model_directory.Creator;
                                    model_teacher_auth.CreateTime = DateTime.Now;
                                    bll_dms_auth.Add(model_teacher_auth);
                                }
                            }

                            // 递归调用
                            CreateDirectory(model, data, directoryId, _deptId, ftpConfig);
                        }
                        index++;
                    }
                }
            }
        }

        #region FTP配置和操作相关方法

        /// <summary>
        /// 检查目录是否存在（本地+FTP）
        /// </summary>
        /// <param name="parentId">父目录ID</param>
        /// <param name="directoryName">目录名称</param>
        /// <param name="config">FTP配置</param>
        /// <returns>是否存在</returns>
        private bool CheckDirectoryExists(int parentId, string directoryName)
        {
            // 使用BLL层方法检查数据库中是否存在同名目录
            return bll_dms_directory.ExistsDirectoryName(parentId, directoryName);
        }

        /// <summary>
        /// 检查文件是否存在（本地+FTP）
        /// </summary>
        /// <param name="directoryId">目录ID</param>
        /// <param name="fileName">文件名称（不包含扩展名）</param>
        /// <param name="config">FTP配置</param>
        /// <returns>是否存在</returns>
        private bool CheckFileExists(int directoryId, string fileName)
        {
            return bll_dms_files.ExistsFileName(directoryId, fileName);
        }

        /// <summary>
        /// 根据ColumnPath向上查找FTP配置
        /// </summary>
        /// <param name="columnPath">地区路径，例如：1|2|3|4</param>
        /// <returns>FTP配置信息</returns>
        private DMS.Model.dms_config GetFtpConfigByColumnPath(string columnPath)
        {
            if (string.IsNullOrEmpty(columnPath))
                return null;

            try
            {
                return bll_dms_config.GetModel(columnPath);
            }
            catch
            {
                // 查询失败时返回null
                return null;
            }
        }

        /// <summary>
        /// 判断是否为FTP服务器
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否为FTP服务器</returns>
        private bool IsFtpServer(DMS.Model.dms_config config)
        {
            if (config == null || string.IsNullOrEmpty(config.ServerUrl))
                return false;

            string strServerUrl = config.ServerUrl.ToLower();
            return strServerUrl.StartsWith("ftp://") || strServerUrl.Contains("ftp");
        }

        /// <summary>
        /// 创建FTP帮助类实例
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>FTP帮助类实例</returns>
        private FtpHelperFluentFTP CreateFtpHelper(DMS.Model.dms_config config)
        {
            if (config == null || !IsFtpServer(config))
                return null;

            string strServer = config.ServerUrl;
            if (strServer.ToLower().StartsWith("ftp://"))
            {
                strServer = strServer.Substring(6);
            }

            return new FtpHelperFluentFTP(strServer, config.FtpAccount, config.FtpPassword, config.FtpPort);
        }

        /// <summary>
        /// 获取FTP根路径
        /// </summary>
        /// <param name="config">FTP配置</param>
        /// <returns>FTP根路径</returns>
        private string GetFtpRootPath(DMS.Model.dms_config config = null)
        {
            if (config != null)
            {
                return $"/dms/doc/{config.ColumnId}/";
            }

            // 如果没有配置，使用当前用户的ColumnId
            return $"/dms/doc/{_ac.modelAreaUser.ColumnID}/";
        }

        /// <summary>
        /// 在FTP服务器上创建目录
        /// </summary>
        /// <param name="ftpPath">FTP路径</param>
        /// <param name="config">FTP配置</param>
        /// <returns>是否成功</returns>
        private bool CreateFtpDirectory(string ftpPath, DMS.Model.dms_config config)
        {
            if (!IsFtpServer(config) || string.IsNullOrEmpty(ftpPath))
                return true; // 非FTP服务器时返回成功

            try
            {
                using (var ftpHelper = CreateFtpHelper(config))
                {
                    if (ftpHelper != null)
                    {
                        return ftpHelper.CreateDirectory(ftpPath);
                    }
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        #endregion

        /// <summary>
        /// 测试FTP连接
        /// </summary>
        /// <param name="context"></param>
        private void TestFtpConnection(HttpContext context)
        {
            context.Response.ContentType = "application/json";

            try
            {
                string strServerUrl = context.Request.Form["serverUrl"];
                string strFtpPort = context.Request.Form["ftpPort"];
                string strFtpAccount = context.Request.Form["ftpAccount"];
                string strFtpPassword = context.Request.Form["ftpPassword"];

                // 验证参数
                if (string.IsNullOrEmpty(strServerUrl) || string.IsNullOrEmpty(strFtpPort) ||
                    string.IsNullOrEmpty(strFtpAccount) || string.IsNullOrEmpty(strFtpPassword))
                {
                    var errorResult = new { success = false, message = "参数不完整" };
                    context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(errorResult));
                    return;
                }

                int nPort;
                if (!int.TryParse(strFtpPort, out nPort) || nPort < 1 || nPort > 65535)
                {
                    var errorResult = new { success = false, message = "端口号无效" };
                    context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(errorResult));
                    return;
                }

                // 提取FTP服务器地址
                string strFtpServer = strServerUrl;
                if (strFtpServer.ToLower().StartsWith("ftp://"))
                {
                    strFtpServer = strFtpServer.Substring(6);
                }

                // 使用FluentFTP进行连接测试
                using (var ftpHelper = new FtpHelperFluentFTP(strFtpServer, strFtpAccount, strFtpPassword, nPort))
                {
                    bool bConnected = ftpHelper.TestConnection();

                    if (bConnected)
                    {
                        // 尝试获取系统信息以进一步验证连接
                        string strSystemType = ftpHelper.GetSystemType();
                        string strCurrentDir = ftpHelper.GetCurrentDirectory();

                        var successResult = new
                        {
                            success = true,
                            message = $"连接成功！服务器类型: {strSystemType}, 当前目录: {strCurrentDir}"
                        };
                        context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(successResult));
                    }
                    else
                    {
                        var failResult = new { success = false, message = "无法连接到FTP服务器" };
                        context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(failResult));
                    }
                }
            }
            catch (Exception ex)
            {
                var errorResult = new
                {
                    success = false,
                    message = DMS.Web.dms.FtpHelperFluentFTP.GetDetailedErrorMessage(ex)
                };
                context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(errorResult));
            }
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}