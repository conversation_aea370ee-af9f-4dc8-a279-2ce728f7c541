using System;
using System.IO;
using System.Security.Cryptography;
using System.Web;
using System.Web.SessionState;

namespace DMS.Web.dms.ajax
{
    /// <summary>
    /// UploadFile 的摘要说明
    /// </summary>
    public class UploadFile : I<PERSON>ttp<PERSON><PERSON><PERSON>, IRequiresSessionState
    {
        /// <summary>
        /// page基类
        /// </summary>
        YunEdu.Authority.AdminCommonJC _ac = new YunEdu.Authority.AdminCommonJC();
        BLL.dms_directory bll_dms_directory = new BLL.dms_directory();
        BLL.dms_config bll_dms_config = new BLL.dms_config();
        BLL.dms_files bll_dms_files = new BLL.dms_files();
        public void ProcessRequest(HttpContext context)
        {
            if (!context.User.Identity.IsAuthenticated)
            {
                context.Response.Write("{\"status\":0,\"msg\":\"登录超时，请重新登录！\"}");
                return;
            }
            string _action = context.Request.QueryString["action"];
            switch (_action)
            {
                case "UpLoadFile":
                    UpLoadFile(context);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="context"></param>
        private void UpLoadFile(HttpContext context)
        {
            HttpPostedFile postedFile = context.Request.Files["Filedata"];
            if (postedFile == null || postedFile.ContentLength == 0)
            {
                context.Response.Write("{\"status\":0,\"msg\":\"请选择要上传的文件！\"}");
                return;
            }
            // 处理文件
            UploadFileInfo uploadFileInfo = new UploadFileInfo();
            uploadFileInfo.PostedFile = postedFile;
            uploadFileInfo.FileName = postedFile.FileName.Substring(postedFile.FileName.LastIndexOf(@"\") + 1);
            uploadFileInfo.FileExt = GetFileExt(postedFile.FileName);
            uploadFileInfo.ContentLength = postedFile.ContentLength;
            uploadFileInfo.SaveName = GetRamCode() + "." + uploadFileInfo.FileExt;
            uploadFileInfo.FileMD5 = GetFileMd5(postedFile);
            context.Response.Write(SaveFile(uploadFileInfo));
        }

        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="uploadFile">上传的图片</param>
        /// <returns></returns>
        public string SaveFile(UploadFileInfo uploadFile)
        {
            try
            {
                // 检查MD5是否已存在
                DMS.Model.dms_files existingFile = bll_dms_files.GetModelByMD5(uploadFile.FileMD5);
                if (existingFile != null)
                {
                    // 文件已存在，检查状态
                    if (existingFile.FileStatus == (int)DMS.BLL.CommonEnum.dms_FileStatus.Completed)
                    {
                        // 文件已上传完成，直接返回文件地址
                        return "{\"status\": 1, \"msg\": \"文件已上传完成！\", \"name\": \"" + existingFile.FileName +
                               "\", \"path\": \"" + existingFile.FilePath + "\", \"thumb\": \"" + existingFile.FilePath +
                               "\", \"size\": " + existingFile.FileSize + ", \"ext\": \"" + existingFile.FileFormat + "\"}";
                    }
                    else
                    {
                        // 文件上传未完成，使用断点续传
                        return ResumeUpload(uploadFile, existingFile);
                    }
                }

                // 从QueryString获取directoryId
                string strDirectoryId = HttpContext.Current.Request.QueryString["directoryId"];
                int nDirectoryId = 0;

                Model.dms_directory directory;
                if (string.IsNullOrEmpty(strDirectoryId) || !int.TryParse(strDirectoryId, out nDirectoryId))
                {
                    // 如果没有directoryId，使用默认根目录
                    directory = bll_dms_directory.GetRoot(_ac.modelAreaUser.ColumnID);
                }
                else
                {
                    directory = bll_dms_directory.GetModel(nDirectoryId);
                }
                if (directory == null)
                {
                    return "{\"status\": 0, \"msg\": \"指定的目录不存在！\"}";
                }

                // 新文件上传
                return UploadNewFile(uploadFile, directory);
            }
            catch (Exception ex)
            {
                return "{\"status\": 0, \"msg\": \"上传过程中发生意外错误：" + ex.Message + "\"}";
            }
        }

        /// <summary>
        /// 返回文件扩展名，不含“.”
        /// </summary>
        /// <param name="_filepath">文件全名称</param>
        /// <returns>string</returns>
        public string GetFileExt(string _filepath)
        {
            if (string.IsNullOrEmpty(_filepath))
            {
                return "";
            }
            if (_filepath.LastIndexOf(".") > 0)
            {
                return _filepath.Substring(_filepath.LastIndexOf(".") + 1); //文件扩展名，不含“.”
            }
            return "";
        }

        /// <summary>
        /// 生成日期随机码
        /// </summary>
        /// <returns></returns>
        public string GetRamCode()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmssffff");
        }

        #region 文件上传

        /// <summary>
        /// 根据ColumnPath向上查找FTP配置
        /// </summary>
        /// <param name="columnPath">地区路径</param>
        /// <returns>FTP配置信息</returns>
        private DMS.Model.dms_config GetFtpConfigByColumnPath(string columnPath)
        {
            if (string.IsNullOrEmpty(columnPath))
                return null;
            try
            {
                return bll_dms_config.GetModel(columnPath);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 判断是否为FTP服务器
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否为FTP服务器</returns>
        private bool IsFtpServer(DMS.Model.dms_config config)
        {
            if (config == null || string.IsNullOrEmpty(config.ServerUrl))
                return false;

            string strServerUrl = config.ServerUrl.ToLower();
            return strServerUrl.StartsWith("ftp://") || strServerUrl.Contains("ftp");
        }

        /// <summary>
        /// 创建FTP帮助类实例
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>FTP帮助类实例</returns>
        private FtpHelperFluentFTP CreateFtpHelper(DMS.Model.dms_config config)
        {
            if (config == null || !IsFtpServer(config))
                return null;

            string strServer = config.ServerUrl;
            if (strServer.ToLower().StartsWith("ftp://"))
            {
                strServer = strServer.Substring(6);
            }

            return new FtpHelperFluentFTP(strServer, config.FtpAccount, config.FtpPassword, config.FtpPort);
        }

        /// <summary>
        /// 获取FTP根路径
        /// </summary>
        /// <param name="config">FTP配置</param>
        /// <returns>FTP根路径</returns>
        private string GetFtpRootPath(DMS.Model.dms_config config)
        {
            if (config != null)
            {
                return $"/dms/doc/{config.ColumnId}/";
            }

            return $"/dms/doc/{_ac.modelAreaUser.ColumnID}/";
        }

        /// <summary>
        /// 计算文件MD5值
        /// </summary>
        /// <param name="postedFile">上传的文件</param>
        /// <returns>MD5值</returns>
        private string GetFileMd5(HttpPostedFile postedFile)
        {
            // 不能释放stream，因为后续还要进行上传操作
            var stream = postedFile.InputStream;
            stream.Position = 0; // 重置流位置
            using (var md5 = MD5.Create())
            {
                byte[] hash = md5.ComputeHash(stream);
                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// 断点续传
        /// </summary>
        private string ResumeUpload(UploadFileInfo postedFile, DMS.Model.dms_files existingFile)
        {
            try
            {
                // 获取目录信息
                Model.dms_directory directory = bll_dms_directory.GetModel(existingFile.DirectoryId);
                if (directory == null)
                {
                    return "{\"status\": 0, \"msg\": \"目录不存在！\"}";
                }

                // 获取FTP配置
                DMS.Model.dms_config ftpConfig = GetFtpConfigByColumnPath(directory.ColumnPath);

                if (IsFtpServer(ftpConfig))
                {
                    // FTP断点续传
                    return ResumeFtpUpload(postedFile, existingFile, ftpConfig);
                }
                else
                {
                    // 本地断点续传（简化处理，重新上传）
                    return ResumeLocalUpload(postedFile, existingFile);
                }
            }
            catch (Exception ex)
            {
                return "{\"status\": 0, \"msg\": \"断点续传失败：" + ex.Message + "\"}";
            }
        }

        /// <summary>
        /// FTP断点续传
        /// </summary>
        /// <param name="uploadFile">要上传的文件</param>
        /// <param name="existingFile">数据库存储的文件信息</param>
        /// <param name="ftpConfig">ftp服务器配置对象</param>
        /// <returns></returns>
        private string ResumeFtpUpload(UploadFileInfo uploadFile, Model.dms_files existingFile, Model.dms_config ftpConfig)
        {
            try
            {
                using (var ftpHelper = CreateFtpHelper(ftpConfig))
                {
                    if (ftpHelper == null)
                    {
                        return "{\"status\": 0, \"msg\": \"无法连接到FTP服务器！\"}";
                    }

                    // 获取远程文件大小
                    long remoteFileSize = ftpHelper.GetFileSize(existingFile.FilePath);

                    if (remoteFileSize >= uploadFile.ContentLength)
                    {
                        // 文件已完整，更新状态
                        bll_dms_files.UpdateFileStatus(existingFile.Id, (int)DMS.BLL.CommonEnum.dms_FileStatus.Completed);
                        return "{\"status\": 1, \"msg\": \"文件上传完成！\", \"name\": \"" + existingFile.FileName +
                               "\", \"path\": \"" + existingFile.FilePath + "\", \"thumb\": \"" + existingFile.FilePath +
                               "\", \"size\": " + existingFile.FileSize + ", \"ext\": \"" + existingFile.FileFormat + "\"}";
                    }

                    using (var fileStream = uploadFile.PostedFile.InputStream)
                    {
                        fileStream.Seek(remoteFileSize, SeekOrigin.Begin);
                        if (ftpHelper.UploadFileFromStreamWithResume(uploadFile.PostedFile.InputStream, existingFile.FilePath))
                        {
                            // 更新文件状态为完成
                            bll_dms_files.UpdateFileStatus(existingFile.Id, (int)DMS.BLL.CommonEnum.dms_FileStatus.Completed);

                            return "{\"status\": 1, \"msg\": \"断点续传完成！\", \"name\": \"" + uploadFile.FileName +
                                   "\", \"path\": \"" + existingFile.FilePath + "\", \"thumb\": \"" + existingFile.FilePath +
                                   "\", \"size\": " + uploadFile.ContentLength + ", \"ext\": \"" + uploadFile.FileExt + "\"}";
                        }
                        else
                        {
                            return "{\"status\": 0, \"msg\": \"FTP断点续传失败！\"}";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return "{\"status\": 0, \"msg\": \"FTP断点续传错误：" + ex.Message + "\"}";
            }
        }

        /// <summary>
        /// 本地断点续传（简化处理）
        /// </summary>
        /// <param name="uploadFile">要上传的文件</param>
        /// <param name="existingFile">数据库存储的文件信息</param>
        /// <returns></returns>
        private string ResumeLocalUpload(UploadFileInfo uploadFile, DMS.Model.dms_files existingFile)
        {
            try
            {
                // 本地文件断点续传比较复杂，这里简化处理：重新上传
                string strAbsolutePath = HttpContext.Current.Server.MapPath(existingFile.FilePath);

                // 删除原文件
                if (File.Exists(strAbsolutePath))
                {
                    File.Delete(strAbsolutePath);
                }

                // 重新上传
                uploadFile.PostedFile.SaveAs(strAbsolutePath);

                // 更新文件状态为完成
                bll_dms_files.UpdateFileStatus(existingFile.Id, (int)DMS.BLL.CommonEnum.dms_FileStatus.Completed);

                return "{\"status\": 1, \"msg\": \"文件重新上传完成！\", \"name\": \"" + uploadFile.FileName +
                       "\", \"path\": \"" + existingFile.FilePath + "\", \"thumb\": \"" + existingFile.FilePath +
                       "\", \"size\": " + uploadFile.ContentLength + ", \"ext\": \"" + uploadFile.FileExt + "\"}";
            }
            catch (Exception ex)
            {
                return "{\"status\": 0, \"msg\": \"本地重新上传错误：" + ex.Message + "\"}";
            }
        }

        /// <summary>
        /// 上传新文件
        /// </summary>
        private string UploadNewFile(UploadFileInfo postedFile, Model.dms_directory directory)
        {
            // 获取FTP配置
            DMS.Model.dms_config ftpConfig = GetFtpConfigByColumnPath(directory.ColumnPath);

            if (IsFtpServer(ftpConfig))
            {
                // FTP服务器上传
                return UploadNewFileToFtp(postedFile, directory, ftpConfig);
            }
            else
            {
                // 本地服务器上传
                return UploadNewFileToLocal(postedFile, directory);
            }
        }

        /// <summary>
        /// 上传新文件到FTP服务器
        /// </summary>
        private string UploadNewFileToFtp(UploadFileInfo uploadFile, Model.dms_directory directory, Model.dms_config ftpConfig)
        {
            try
            {
                // 获取FTP根路径
                string strFtpRootPath = GetFtpRootPath(ftpConfig);
                string strFtpFilePath = $"{strFtpRootPath.TrimEnd('/')}/{uploadFile.SaveName}";
                uploadFile.FilePath = strFtpFilePath;

                // 先保存文件记录到数据库（状态为上传中）
                Guid fileId = SaveFileRecord(uploadFile, directory, (int)BLL.CommonEnum.dms_FileStatus.Uploading);

                if (fileId == Guid.Empty)
                {
                    return "{\"status\": 0, \"msg\": \"保存文件记录失败！\"}";
                }

                using (var ftpHelper = CreateFtpHelper(ftpConfig))
                {
                    if (ftpHelper == null)
                    {
                        return "{\"status\": 0, \"msg\": \"无法连接到FTP服务器！\"}";
                    }

                    // 上传文件
                    using (var fileStream = uploadFile.PostedFile.InputStream)
                    {
                        // 重置位置
                        fileStream.Position = 0;
                        if (ftpHelper.UploadFileFromStream(fileStream, strFtpFilePath))
                        {
                            // 更新文件状态为完成
                            bll_dms_files.UpdateFileStatus(fileId, (int)DMS.BLL.CommonEnum.dms_FileStatus.Completed);

                            return "{\"status\": 1, \"msg\": \"上传文件成功！\", \"name\": \"" + uploadFile.FileName +
                                   "\", \"path\": \"" + uploadFile.FilePath + "\", \"thumb\": \"" + uploadFile.FilePath +
                                   "\", \"size\": " + uploadFile.ContentLength + ", \"ext\": \"" + uploadFile.FileExt + "\"}";
                        }
                        else
                        {
                            return "{\"status\": 0, \"msg\": \"FTP上传失败！\"}";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return "{\"status\": 0, \"msg\": \"FTP上传错误：" + ex.Message + "\"}";
            }
        }

        /// <summary>
        /// 上传新文件到本地服务器
        /// </summary>
        private string UploadNewFileToLocal(UploadFileInfo uploadFile, Model.dms_directory directory)
        {
            try
            {
                // 构建本地路径
                string strLocalPath = bll_dms_directory.GetRootDirectory(_ac.modelAreaUser.ColumnID) + uploadFile.SaveName;

                // 先保存文件记录到数据库（状态为上传中）
                Guid fileId = SaveFileRecord(uploadFile, directory, (int)DMS.BLL.CommonEnum.dms_FileStatus.Uploading);

                if (fileId == Guid.Empty)
                {
                    return "{\"status\": 0, \"msg\": \"保存文件记录失败！\"}";
                }

                // 确保目录存在
                string strAbsolutePath = HttpContext.Current.Server.MapPath(strLocalPath);
                string strDirectoryPath = Path.GetDirectoryName(strAbsolutePath);
                if (!Directory.Exists(strDirectoryPath))
                {
                    Directory.CreateDirectory(strDirectoryPath);
                }

                // 保存文件
                uploadFile.PostedFile.SaveAs(strAbsolutePath);

                // 更新文件状态为完成
                bll_dms_files.UpdateFileStatus(fileId, (int)DMS.BLL.CommonEnum.dms_FileStatus.Completed);

                return "{\"status\": 1, \"msg\": \"上传文件成功！\", \"name\": \"" + uploadFile.FileName +
                       "\", \"path\": \"" + strLocalPath + "\", \"thumb\": \"" + strLocalPath +
                       "\", \"size\": " + uploadFile.ContentLength + ", \"ext\": \"" + uploadFile.FileExt + "\"}";
            }
            catch (Exception ex)
            {
                return "{\"status\": 0, \"msg\": \"本地上传错误：" + ex.Message + "\"}";
            }
        }

        /// <summary>
        /// 保存文件记录到数据库
        /// </summary>
        private Guid SaveFileRecord(UploadFileInfo uploadFile, Model.dms_directory directory, int fileStatus)
        {
            try
            {
                DMS.Model.dms_files fileModel = new DMS.Model.dms_files();
                fileModel.FileName = uploadFile.FileName;
                fileModel.FileFormat = uploadFile.FileExt;
                fileModel.FileSize = uploadFile.ContentLength;
                fileModel.FilePath = uploadFile.FilePath;
                fileModel.FileMD5 = uploadFile.FileMD5;
                fileModel.FileStatus = fileStatus;
                fileModel.Creator = new Guid(_ac.UserId);
                fileModel.CreateTime = DateTime.Now;
                fileModel.IsOpen = 0; // 默认不公开
                fileModel.FileType = bll_dms_files.getFileType(uploadFile.FileExt);
                fileModel.DirectoryId = directory.Id;
                fileModel.DirectoryPath = directory.Path;
                fileModel.ColumnId = directory.ColumnId;
                fileModel.ColumnPath = directory.ColumnPath;
                return bll_dms_files.Add(fileModel) ? fileModel.Id : Guid.Empty;
            }
            catch
            {
                return Guid.Empty;
            }
        }

        #endregion

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }

        /// <summary>
        /// 上传文件信息
        /// </summary>
        public class UploadFileInfo
        {
            /// <summary>
            ///  上传的文件
            /// </summary>
            public HttpPostedFile PostedFile { get; set; }

            /// <summary>
            /// 文件名
            /// </summary>
            public string FileName { get; set; }

            /// <summary>
            /// 文件存储名
            /// </summary>
            public string SaveName { get; set; }

            /// <summary>
            /// 文件扩展名
            /// </summary>
            public string FileExt { get; set; }

            /// <summary>
            /// 文件长度
            /// </summary>
            public int ContentLength { get; set; }

            /// <summary>
            /// 文件MD5值
            /// </summary>
            public string FileMD5 { get; set; }

            /// <summary>
            /// 文件在服务器的路径
            /// </summary>
            public string FilePath { get; set; }
        }
    }


}