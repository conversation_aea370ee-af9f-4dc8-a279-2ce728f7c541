<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="md5_upload_test.aspx.cs" Inherits="DMS.Web.dms.md5_upload_test" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>MD5文件上传测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 8px 15px; margin: 5px; background-color: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .result { margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 3px; white-space: pre-wrap; }
        .upload-form { margin: 10px 0; }
        .upload-form input[type="file"] { margin: 5px; }
        .upload-form input[type="text"] { margin: 5px; padding: 5px; width: 100px; }
        .status-uploading { color: orange; }
        .status-completed { color: green; }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h2>MD5文件上传和断点续传测试</h2>
            
            <div class="test-section">
                <h3>文件状态枚举测试</h3>
                <asp:Button ID="btnTestEnum" runat="server" Text="测试文件状态枚举" CssClass="btn" OnClick="btnTestEnum_Click" />
                <div class="result">
                    <asp:Literal ID="litEnumResult" runat="server"></asp:Literal>
                </div>
            </div>
            
            <div class="test-section">
                <h3>MD5查询测试</h3>
                <asp:TextBox ID="txtMD5" runat="server" placeholder="输入MD5值" Width="300px"></asp:TextBox>
                <asp:Button ID="btnQueryMD5" runat="server" Text="查询MD5" CssClass="btn" OnClick="btnQueryMD5_Click" />
                <div class="result">
                    <asp:Literal ID="litMD5Result" runat="server"></asp:Literal>
                </div>
            </div>
            
            <div class="test-section">
                <h3>文件上传测试（带MD5检查）</h3>
                <div class="upload-form">
                    <input type="file" id="fileInput" />
                    <input type="text" id="txtDirectoryId" placeholder="目录ID" value="1" />
                    <button type="button" id="btnUpload" class="btn">上传文件</button>
                </div>
                <div class="result" id="uploadResult"></div>
            </div>
            
            <div class="test-section">
                <h3>数据库文件记录查询</h3>
                <asp:Button ID="btnQueryFiles" runat="server" Text="查询最近上传的文件" CssClass="btn" OnClick="btnQueryFiles_Click" />
                <div class="result">
                    <asp:Literal ID="litFilesResult" runat="server"></asp:Literal>
                </div>
            </div>
        </div>
    </form>

    <script>
        $(document).ready(function() {
            $('#btnUpload').click(function() {
                var fileInput = $('#fileInput')[0];
                var directoryId = $('#txtDirectoryId').val();
                
                if (!fileInput.files.length) {
                    $('#uploadResult').text('请选择文件');
                    return;
                }
                
                if (!directoryId) {
                    $('#uploadResult').text('请输入目录ID');
                    return;
                }
                
                var formData = new FormData();
                formData.append('file', fileInput.files[0]);
                
                $('#uploadResult').text('正在上传...');
                
                $.ajax({
                    url: '/dms/ajax/UploadFile.ashx?directoryId=' + directoryId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        try {
                            var result = JSON.parse(response);
                            var resultText = '上传结果:\n';
                            resultText += 'Status: ' + result.status + '\n';
                            resultText += 'Message: ' + result.msg + '\n';
                            if (result.path) {
                                resultText += 'Path: ' + result.path + '\n';
                            }
                            if (result.size) {
                                resultText += 'Size: ' + result.size + ' bytes\n';
                            }
                            $('#uploadResult').text(resultText);
                        } catch (e) {
                            $('#uploadResult').text('上传结果:\n' + response);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#uploadResult').text('上传失败: ' + error);
                    }
                });
            });
        });
    </script>
</body>
</html>
