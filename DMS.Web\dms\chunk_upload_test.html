<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>分片上传测试</title>
    <style>
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s;
        }
        .log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>分片上传测试</h2>
        
        <div>
            <label>选择文件:</label>
            <input type="file" id="fileInput" />
        </div>
        
        <div>
            <label>目录ID:</label>
            <input type="number" id="directoryId" value="1" />
        </div>
        
        <div>
            <label>分片大小 (MB):</label>
            <input type="number" id="chunkSize" value="2" min="1" max="10" />
        </div>
        
        <div>
            <button class="btn-primary" onclick="startUpload()">开始上传</button>
            <button class="btn-warning" onclick="pauseUpload()">暂停上传</button>
            <button class="btn-success" onclick="resumeUpload()">继续上传</button>
            <button onclick="queryStatus()">查询状态</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div>
            <label>上传进度:</label>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <span id="progressText">0%</span>
        </div>
        
        <div>
            <label>日志:</label>
            <div class="log" id="logArea"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/spark-md5@3.0.2/spark-md5.min.js"></script>
    <script>
        let currentFile = null;
        let fileMD5 = null;
        let totalChunks = 0;
        let uploadedChunks = [];
        let isUploading = false;
        let isPaused = false;
        let chunkSizeBytes = 2 * 1024 * 1024; // 2MB

        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        function updateProgress(uploaded, total) {
            const percent = Math.round((uploaded / total) * 100);
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = percent + '%';
        }

        async function calculateMD5(file) {
            return new Promise((resolve, reject) => {
                const spark = new SparkMD5.ArrayBuffer();
                const fileReader = new FileReader();
                const chunkSize = 2097152; // 2MB
                let currentChunk = 0;
                const chunks = Math.ceil(file.size / chunkSize);

                fileReader.onload = function(e) {
                    spark.append(e.target.result);
                    currentChunk++;

                    if (currentChunk < chunks) {
                        loadNext();
                    } else {
                        resolve(spark.end());
                    }
                };

                fileReader.onerror = function() {
                    reject(new Error('文件读取失败'));
                };

                function loadNext() {
                    const start = currentChunk * chunkSize;
                    const end = Math.min(start + chunkSize, file.size);
                    fileReader.readAsArrayBuffer(file.slice(start, end));
                }

                loadNext();
            });
        }

        async function startUpload() {
            const fileInput = document.getElementById('fileInput');
            const directoryId = document.getElementById('directoryId').value;
            const chunkSizeMB = document.getElementById('chunkSize').value;
            
            if (!fileInput.files.length) {
                alert('请选择文件');
                return;
            }

            currentFile = fileInput.files[0];
            chunkSizeBytes = chunkSizeMB * 1024 * 1024;
            totalChunks = Math.ceil(currentFile.size / chunkSizeBytes);
            
            log(`开始上传文件: ${currentFile.name}`);
            log(`文件大小: ${(currentFile.size / 1024 / 1024).toFixed(2)} MB`);
            log(`分片数量: ${totalChunks}`);
            
            try {
                log('正在计算文件MD5...');
                fileMD5 = await calculateMD5(currentFile);
                log(`文件MD5: ${fileMD5}`);
                
                // 查询上传状态
                await queryStatus();
                
                // 开始上传
                isUploading = true;
                isPaused = false;
                await uploadChunks(directoryId);
                
            } catch (error) {
                log(`错误: ${error.message}`);
            }
        }

        async function uploadChunks(directoryId) {
            for (let i = 0; i < totalChunks && isUploading && !isPaused; i++) {
                if (uploadedChunks.includes(i)) {
                    log(`分片 ${i + 1} 已存在，跳过`);
                    continue;
                }

                try {
                    const start = i * chunkSizeBytes;
                    const end = Math.min(start + chunkSizeBytes, currentFile.size);
                    const chunk = currentFile.slice(start, end);
                    
                    log(`上传分片 ${i + 1}/${totalChunks}`);
                    
                    const formData = new FormData();
                    formData.append('action', 'upload');
                    formData.append('fileMD5', fileMD5);
                    formData.append('fileName', currentFile.name);
                    formData.append('chunkIndex', i);
                    formData.append('totalChunks', totalChunks);
                    formData.append('fileSize', currentFile.size);
                    formData.append('directoryId', directoryId);
                    formData.append('file', chunk);

                    const response = await fetch('ajax/uploader.ashx', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        uploadedChunks.push(i);
                        updateProgress(uploadedChunks.length, totalChunks);
                        log(`分片 ${i + 1} 上传成功`);
                    } else {
                        throw new Error(result.message);
                    }
                    
                } catch (error) {
                    log(`分片 ${i + 1} 上传失败: ${error.message}`);
                    isUploading = false;
                    return;
                }
            }

            if (uploadedChunks.length === totalChunks && !isPaused) {
                log('所有分片上传完成，开始合并文件...');
                await mergeChunks(directoryId);
            }
        }

        async function mergeChunks(directoryId) {
            try {
                const formData = new FormData();
                formData.append('action', 'merge');
                formData.append('fileMD5', fileMD5);
                formData.append('directoryId', directoryId);

                const response = await fetch('ajax/uploader.ashx', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    log('文件合并成功！');
                    log(`文件ID: ${result.data.fileId}`);
                    log(`文件路径: ${result.data.filePath}`);
                    isUploading = false;
                } else {
                    throw new Error(result.message);
                }
                
            } catch (error) {
                log(`文件合并失败: ${error.message}`);
            }
        }

        async function queryStatus() {
            if (!fileMD5) {
                log('请先选择文件并计算MD5');
                return;
            }

            try {
                const response = await fetch(`ajax/uploader.ashx?action=status&fileMD5=${fileMD5}`);
                const result = await response.json();
                
                if (result.success) {
                    if (result.data.completed) {
                        log('文件已存在，可以秒传');
                        log(`文件ID: ${result.data.fileId}`);
                        log(`文件路径: ${result.data.filePath}`);
                        updateProgress(1, 1);
                    } else if (result.data.exists) {
                        uploadedChunks = result.data.uploadedChunks;
                        log(`找到已上传的分片: ${uploadedChunks.length}/${result.data.totalChunks}`);
                        updateProgress(uploadedChunks.length, result.data.totalChunks);
                    } else {
                        log('文件未开始上传');
                        uploadedChunks = [];
                        updateProgress(0, 1);
                    }
                } else {
                    throw new Error(result.message);
                }
                
            } catch (error) {
                log(`查询状态失败: ${error.message}`);
            }
        }

        function pauseUpload() {
            isPaused = true;
            log('上传已暂停');
        }

        function resumeUpload() {
            if (isPaused && currentFile) {
                isPaused = false;
                log('继续上传...');
                const directoryId = document.getElementById('directoryId').value;
                uploadChunks(directoryId);
            }
        }
    </script>
</body>
</html>
