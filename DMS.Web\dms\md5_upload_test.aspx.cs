using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;

namespace DMS.Web.dms
{
    public partial class md5_upload_test : System.Web.UI.Page
    {
        private YunEdu.Authority.AdminCommonJC ac = new YunEdu.Authority.AdminCommonJC();
        private DMS.BLL.dms_files bllFiles = new DMS.BLL.dms_files();
        private DMS.BLL.dms_directory bllDirectory = new DMS.BLL.dms_directory();

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnTestEnum_Click(object sender, EventArgs e)
        {
            try
            {
                string strResult = "文件状态枚举测试:\n\n";
                
                // 测试枚举值
                strResult += $"上传中状态值: {(int)DMS.BLL.CommonEnum.dms_FileStatus.Uploading}\n";
                strResult += $"上传中描述: {DMS.BLL.CommonEnum.Getdms_FileStatus(((int)DMS.BLL.CommonEnum.dms_FileStatus.Uploading).ToString())}\n\n";
                
                strResult += $"上传完成状态值: {(int)DMS.BLL.CommonEnum.dms_FileStatus.Completed}\n";
                strResult += $"上传完成描述: {DMS.BLL.CommonEnum.Getdms_FileStatus(((int)DMS.BLL.CommonEnum.dms_FileStatus.Completed).ToString())}\n\n";

                litEnumResult.Text = strResult;
            }
            catch (Exception ex)
            {
                litEnumResult.Text = $"测试失败: {ex.Message}";
            }
        }

        protected void btnQueryMD5_Click(object sender, EventArgs e)
        {
            try
            {
                string strMD5 = txtMD5.Text.Trim();
                if (string.IsNullOrEmpty(strMD5))
                {
                    litMD5Result.Text = "请输入MD5值";
                    return;
                }

                var file = bllFiles.GetModelByMD5(strMD5);
                if (file == null)
                {
                    litMD5Result.Text = "未找到对应的文件记录";
                    return;
                }

                string strResult = $"文件信息:\n";
                strResult += $"- ID: {file.Id}\n";
                strResult += $"- 文件名: {file.FileName}\n";
                strResult += $"- 文件格式: {file.FileFormat}\n";
                strResult += $"- 文件路径: {file.FilePath}\n";
                strResult += $"- 文件大小: {file.FileSize} bytes\n";
                strResult += $"- MD5: {file.FileMD5}\n";
                strResult += $"- 状态: {file.FileStatus} ({DMS.BLL.CommonEnum.Getdms_FileStatus(file.FileStatus.ToString())})\n";
                strResult += $"- 创建时间: {file.CreateTime}\n";
                strResult += $"- 目录ID: {file.DirectoryId}\n";
                strResult += $"- ColumnId: {file.ColumnId}\n";

                litMD5Result.Text = strResult;
            }
            catch (Exception ex)
            {
                litMD5Result.Text = $"查询失败: {ex.Message}";
            }
        }

        protected void btnQueryFiles_Click(object sender, EventArgs e)
        {
            try
            {
                // 查询最近上传的10个文件
                string strWhere = $"ColumnId = {ac.modelAreaUser.ColumnID}";
                DataSet ds = bllFiles.GetListByPage(strWhere, "CreateTime DESC", 1, 10);
                
                if (ds.Tables[0].Rows.Count == 0)
                {
                    litFilesResult.Text = "没有找到文件记录";
                    return;
                }

                string strResult = $"最近上传的文件 (共{ds.Tables[0].Rows.Count}个):\n\n";
                
                foreach (DataRow row in ds.Tables[0].Rows)
                {
                    strResult += $"文件名: {row["FileName"]}\n";
                    strResult += $"格式: {row["FileFormat"]}\n";
                    strResult += $"大小: {row["FileSize"]} bytes\n";
                    strResult += $"MD5: {row["FileMD5"]}\n";
                    
                    int nStatus = Convert.ToInt32(row["FileStatus"]);
                    string strStatusDesc = DMS.BLL.CommonEnum.Getdms_FileStatus(nStatus.ToString());
                    strResult += $"状态: {nStatus} ({strStatusDesc})\n";
                    
                    strResult += $"创建时间: {row["CreateTime"]}\n";
                    strResult += $"路径: {row["FilePath"]}\n";
                    strResult += "---\n\n";
                }

                litFilesResult.Text = strResult;
            }
            catch (Exception ex)
            {
                litFilesResult.Text = $"查询失败: {ex.Message}";
            }
        }
    }
}
