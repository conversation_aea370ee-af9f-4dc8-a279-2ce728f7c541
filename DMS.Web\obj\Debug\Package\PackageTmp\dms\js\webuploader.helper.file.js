﻿// 封装组件 闭包环境
(function (window, $) {
    // 文件上传相关参数
    const uploadDefaultOptions = {
        // 文件数量限制
        fileNumLimit: 5,
        // 是否多文件
        multiple: false,
        // 添加水印
        water: false,
        // 生成缩略图
        thumbnail: false,
        // 删除文件时是否删除远程服务器文件
        deleteServerFile: true,
        // 文件大小限制
        fileSizeLimit: 200 * 1024 * 1024,    //200 M
        // swf文件路径
        swf: './uploader.swf',
        // 文件类型
        accept: 'jpg,jpge,png,gif,xls,xlsx,doc,docx,ppt,pptx,pdf,zip,rar,7z',
        // 文件接收服务端。
        server: '',
        // 选择文件的按钮。可选。
        // 按钮文字
        btnText: "选择文件",
        // 已上传的文件列表
        fileList: [],
        // 上传文件成功后的回调函数
        onSuccess: null,
        // 上传成功的文件移除后的回调函数
        onRemove: null,
        // 是否启用分片上传，默认启用
        chunked: true,
        // 分片大小(MB)，默认2MB
        chunkSize: 2,
        // 目标目录ID
        directoryId: 1,
    }

    class Uploader {
        constructor(element, options) {
            // 判断element是否有值
            if (!element) {
                console.error("初始化上传控件需要指定dom元素");
                return;
            }
            // 判断options中server是否有值
            if (!options.server || options.server === '') {
                console.error("初始化上传控件需要指定server参数");
                return;
            }
            // 判断element是否是字符串
            if (typeof element === 'string') {
                this.element = $(element);
            }
            else if (element instanceof HTMLElement) {
                this.element = $(element);
            }
            else if (element instanceof jQuery) {
                this.element = element;
            }
            else {
                console.error("初始化上传控件发生错误:element参数类型错误");
                return;
            }
            // 合并默认参数
            this.options = $.extend({}, uploadDefaultOptions, options);
            // 初始设置已上传文件数量为0
            this.fileNum = 0;
            // 初始化
            this._init();
        }

        _init() {
            // 判断是否有layer组件
            if (window.layer) {
                this.showMsg = layer.msg;
            }
            else {
                this.showMsg = function (msg) {
                    top.alert(msg);
                };
            }
            // 初始化上传控件
            this._initUploader();
        }

        /**
             * 创建进度条
             */
        _createProgressBar = () => {
            let progress = $('<div class="upload-list__progress"></div>');
            let progressBarOuter = $('<div class="upload-list__progress__outer"></div>');
            let progressBarInner = $('<div class="upload-list__progress__inner"></div>');
            let progressBarText = $('<div class="upload-list__progress__text"></div>');
            progressBarText.text('0%');
            progressBarOuter.append(progressBarInner);
            progress.append(progressBarOuter, progressBarText);
            return progress;
        };

        /**
         * 创建上传列表项
         * @param file 待上传文件信息
         * @param isUploaded 是否是已上传完的文件
         * @returns {*|jQuery|HTMLElement}
         * @private
         */
        _createUploadItem = (file, isUploaded = false) => {
            let item = $('<div class="upload-list__item"></div>').attr('id', file.id);
            let itemInfo = $('<div class="upload-list__item-info"></div>');
            let itemStatus = $('<div class="upload-list__item-status"></div>');
            let itemCancel = $('<div class="upload-list__item-close"><img src="/admin/images/delete.png" alt="" /></div>');
            item.append(itemInfo, itemStatus, itemCancel);
            let title = $('<div class="upload-list__title"><span>' + file.name + '</span></div>');
            itemInfo.append(title);
            // 文件上传状态不存在时，创建进度条
            if (!isUploaded) {
                let progress = this._createProgressBar();
                itemInfo.append(progress);
            }
            else {
                this._setUploadItemStatus(item, 1);
            }
            itemStatus.append($('<img src="/admin/images/true.gif" alt="" />'));
            itemCancel.on('click', () => {
                let fileStatus = item.data('status');
                item.addClass('upload-list__item-leave');
                // 判断创建上传列表项时是否是已经上传完的状态
                if (!isUploaded) {
                    // 删除文件
                    this.uploader.removeFile(file);
                }
                if (this.options.deleteServerFile) {
                    // 通知服务器删除文件
                    this._deleteFile(fileStatus);
                }
                this.fileNum--;
                // 延迟删除,让动画执行完
                setTimeout(function () {
                    item.remove();
                }, 500);
                if (typeof this.options.onRemove === 'function') {
                    this.options.onRemove(fileStatus);
                }
            });
            this.uploadList.append(item);
            this.fileNum++;
            return item;
        }

        /**
         * 设置上传状态
         */
        _setUploadItemStatus = (dom, status) => {
            dom.addClass(status === 1 ? 'success' : 'error');
        }

        _initUploader() {
            // 判断是否启用分片上传
            const isChunkUpload = this.options.chunked !== false;

            // 构建服务器URL
            let serverUrl = this.options.server;
            let queryParams = '';

            if (isChunkUpload) {
                // 使用分片上传接口
                queryParams += 'action=upload';
            } else {
                // 使用传统上传接口
                queryParams += 'action=UpLoadFile';

                if (this.options.water) {
                    queryParams += '&IsWater=1';
                }
                if (this.options.thumbnail) {
                    queryParams += '&IsThumbnail=1';
                }
            }

            if (queryParams !== '') {
                if (serverUrl.includes('?')) {
                    serverUrl = serverUrl + '&' + queryParams;
                } else {
                    serverUrl = serverUrl + '?' + queryParams;
                }
            }

            this.options.server = serverUrl;
            // 创建上传按钮
            let btnDom = $('<div class="upload-btn"></div>').text(this.options.btnText).appendTo(this.element);
            // 创建展示列表
            this.uploadList = $('<div class="upload-list"></div>').appendTo(this.element);

            // 配置WebUploader
            let uploaderConfig = {
                auto: false, // 改为手动上传，以便计算MD5和检查状态
                swf: this.options.swf,
                server: this.options.server,
                pick: {
                    id: btnDom,
                    multiple: this.options.multiple
                },
                accept: {
                    extensions: this.options.accept
                },
                compress: false,
                fileNumLimit: this.options.fileNumLimit,
                fileSingleSizeLimit: this.options.fileSizeLimit,
                fileVal: isChunkUpload ? 'file' : 'Filedata'
            };

            // 分片上传配置
            if (isChunkUpload) {
                uploaderConfig.chunked = true;
                uploaderConfig.chunkSize = (this.options.chunkSize || 2) * 1024 * 1024;
                uploaderConfig.threads = 1; // 串行上传，避免并发问题
                uploaderConfig.formData = {
                    'directoryId': this.options.directoryId
                };
            } else {
                uploaderConfig.chunked = false;
                uploaderConfig.threads = 3;
                uploaderConfig.formData = this.formData || {};
            }

            let uploader = WebUploader.create(uploaderConfig);

            // 如果启用分片上传，需要设置断点续传逻辑
            if (isChunkUpload) {
                this._setupChunkResumeLogic(uploader);
            }
            var self = this;
            // 当验证不通过时，会以派送错误事件的形式通知
            uploader.on('error', function (type) {
                switch (type) {
                    case 'Q_EXCEED_NUM_LIMIT':
                        self.showMsg(`最多允许上传${self.options.fileNumLimit}个文件`);
                        break;
                    case 'Q_EXCEED_SIZE_LIMIT':
                        self.showMsg("文件总大小超出限制！");
                        break;
                    case 'F_EXCEED_SIZE':
                        self.showMsg("文件大小超出限制！");
                        break;
                    case 'Q_TYPE_DENIED':
                        self.showMsg("禁止上传该类型文件！");
                        break;
                    case 'F_DUPLICATE':
                        self.showMsg("请勿重复上传该文件！");
                        break;
                    default:
                        self.showMsg('错误代码：' + type);
                        break;
                }
            });

            // 当文件被加入队列之前触发。如果此事件handler的返回值为false，则此文件不会被添加进入队列。
            uploader.on('beforeFileQueued', function (file) {
                if (self.options.fileNumLimit) {
                    if (self.fileNum >= self.options.fileNumLimit) {
                        self.showMsg(`最多允许上传${self.options.fileNumLimit}个文件`);
                        return false;
                    }
                }
            });

            //当有文件添加进来的时候
            uploader.on('fileQueued', function (file) {
                // 创建上传的文件列表
                let uploadItem = self._createUploadItem(file);

                if (isChunkUpload) {
                    // 分片上传：先计算MD5，再检查状态
                    file._uploadItem = uploadItem; // 保存上传项引用
                    self._handleChunkUpload(file, uploader);
                } else {
                    // 传统上传：直接上传
                    uploader.upload(file);
                }
            });

            //文件上传过程中创建进度条实时显示
            uploader.on('uploadProgress', function (file, percentage) {
                let item = self.uploadList.find(`#${file.id}`);
                let progressBarInner = item.find('.upload-list__progress__inner');
                let progressBarText = item.find('.upload-list__progress__text');
                progressBarInner.width(percentage * 100 + '%');
                progressBarText.text(Math.round(percentage * 100) + '%');
            });

            //当文件上传出错时触发
            uploader.on('uploadError', function (file, reason) {
                let item = self.uploadList.find(`#${file.id}`);
                item.fadeOut(500, function () {
                    // 移除dom
                    item.remove();
                    // 从队列中移除
                    uploader.removeFile(file);
                    self.showMsg(file.name + "上传失败，错误代码：" + reason);
                })
            });

            //当文件上传成功时触发
            uploader.on('uploadSuccess', function (file, data) {
                let item = self.uploadList.find(`#${file.id}`);

                if (isChunkUpload) {
                    // 分片上传响应处理
                    if (data.success) {
                        if (data.data && data.data.completed) {
                            // 文件上传完成
                            let fileInfo = data.data;
                            self._setUploadItemStatus(item, 1);

                            // 构造兼容的数据格式
                            let compatibleData = {
                                status: 1,
                                path: fileInfo.filePath,
                                thumb: fileInfo.filePath,
                                size: file.size,
                                ext: self._getFileExt(file.name),
                                name: file.name,
                                md5: file.md5 || '',
                                fileId: fileInfo.fileId
                            };

                            item.data('status', compatibleData);

                            // 触发成功回调
                            if (typeof self.options.onSuccess === 'function') {
                                self.options.onSuccess(compatibleData);
                            }
                        } else {
                            // 分片上传中，更新进度
                            self._updateChunkProgress(file, data.data);
                        }
                    } else {
                        // 上传失败
                        self._setUploadItemStatus(item, 0);
                        self.showMsg("上传失败：" + (data.message || "未知错误"));
                    }
                } else {
                    // 传统上传响应处理
                    self._setUploadItemStatus(item, data.status);
                    item.data('status', data);

                    if (typeof self.options.onSuccess === 'function') {
                        self.options.onSuccess(data);
                    }
                }
            });

            //不管成功或者失败，文件上传完成时触发
            uploader.on('uploadComplete', function (file) {
                let progress = self.uploadList.find(`#${file.id} .upload-list__progress`);
                progress.fadeOut(800, function () {
                    progress.remove();
                });
            });


            this._renderUploadedFile();
            this.uploader = uploader;
        }

        _deleteFile = (data) => {
            if (data && data.path) {
                $.ajax({
                    type: 'post',
                    url: '/ajax/upload_file.ashx?action=deleteFile',
                    data: data,
                    success: function (data) {
                        console.log(data);
                    },
                })
            }
        }

        _renderUploadedFile = () => {
            // 处理已经存在的文件
            if (Array.isArray(this.options.fileList) && this.options.fileList.length > 0) {
                this.options.fileList.forEach((item, index) => {
                    // 创建上传的文件列表
                    let uploadItem = this._createUploadItem({ id: `uploaded_${item.name}_${index}`, name: item.name, path: item.path }, true);
                    uploadItem.data('status', item);
                });
            }
        }

        refreshFileList = (data) => {
            this.options.fileList = data;
            this.uploadList.empty();
            this._renderUploadedFile();
        }

        // 设置分片断点续传逻辑
        _setupChunkResumeLogic = (uploader) => {
            const self = this;

            // 重写分片发送前的处理
            uploader.on('uploadBeforeSend', function (object, data, headers) {
                const file = object.file;

                // 检查当前分片是否已经上传过
                if (file.uploadedChunks && file.uploadedChunks.indexOf(data.chunk) !== -1) {
                    // 跳过已上传的分片
                    console.log('跳过已上传分片:', data.chunk);

                    // 模拟分片上传成功
                    setTimeout(function () {
                        uploader.trigger('uploadProgress', file, 1);
                        uploader.trigger('uploadAccept', object, {
                            success: true,
                            data: {
                                uploaded: true,
                                chunkIndex: data.chunk,
                                message: '分片已存在，跳过上传'
                            }
                        });
                    }, 10);

                    // 阻止实际的网络请求
                    return false;
                }

                // 添加分片索引和总数到请求数据
                data.chunkIndex = data.chunk;
                data.totalChunks = data.chunks;
            });

            // 监听分片上传进度
            uploader.on('uploadProgress', function (file, percentage) {
                const uploadItem = file._uploadItem;
                if (uploadItem) {
                    self._updateUploadProgress(uploadItem, file, percentage);
                }
            });
        }

        // 分片上传处理函数
        _handleChunkUpload = (file, uploader) => {
            const self = this;
            const uploadItem = file._uploadItem;

            // 更新状态显示
            self._updateProgressText(uploadItem, "正在计算文件MD5...");

            // 计算文件MD5
            self._calculateFileMD5(file, uploader, function (md5) {
                file.md5 = md5;

                // 查询上传状态
                self._updateProgressText(uploadItem, "检查文件状态...");

                $.ajax({
                    url: self.options.server.replace('action=upload', 'action=status'),
                    type: 'GET',
                    data: { fileMD5: md5 },
                    dataType: 'json',
                    success: function (result) {
                        if (result.success && result.data) {
                            if (result.data.completed) {
                                // 文件已存在，秒传
                                self._updateProgressText(uploadItem, "文件已存在，秒传成功");

                                // 模拟上传成功回调
                                const mockData = {
                                    success: true,
                                    data: {
                                        completed: true,
                                        filePath: result.data.filePath,
                                        fileId: result.data.fileId
                                    }
                                };
                                uploader.trigger('uploadSuccess', file, mockData);
                                return;
                            } else if (result.data.exists && result.data.uploadedChunks) {
                                // 文件有部分分片已上传，实现断点续传
                                const uploadedChunks = result.data.uploadedChunks;
                                const totalChunks = result.data.totalChunks;

                                self._updateProgressText(uploadItem, `发现已上传分片：${uploadedChunks.length}/${totalChunks}，继续上传...`);

                                // 设置断点续传信息
                                file.uploadedChunks = uploadedChunks;
                                file.totalChunks = totalChunks;

                                // 更新进度显示
                                self._updateChunkProgressDisplay(uploadItem, uploadedChunks.length, totalChunks);
                            }
                        }

                        // 设置上传参数
                        self._setupUploadParams(uploader, file, md5);

                        // 开始上传
                        self._updateProgressText(uploadItem, "开始上传文件...");
                        uploader.upload(file);
                    },
                    error: function () {
                        self._updateProgressText(uploadItem, "状态查询失败，直接上传");
                        self._setupUploadParams(uploader, file, md5);
                        uploader.upload(file);
                    }
                });
            }, function (percentage) {
                // MD5计算进度回调
                const percent = Math.round(percentage * 100);
                self._updateProgressText(uploadItem, `计算MD5进度：${percent}%`);
            });
        }

        // 设置上传参数
        _setupUploadParams = (uploader, file, md5) => {
            uploader.options.formData.fileMD5 = md5;
            uploader.options.formData.fileName = file.name;
            uploader.options.formData.fileSize = file.size;
            uploader.options.formData.directoryId = this.options.directoryId;
        }

        // 计算文件MD5
        _calculateFileMD5 = (file, uploader, callback, progressCallback) => {
            try {

                // 使用WebUploader实例的md5File方法计算MD5
                uploader.md5File(file, 0, file.size)
                    .progress(function (percentage) {
                        // 计算进度中，更新进度显示
                        if (progressCallback) {
                            progressCallback(percentage);
                        }
                    }).then(function (md5) {
                        // MD5计算完成
                        callback(md5);
                    });
            } catch (error) {
                console.error('MD5计算异常:', error);
                callback('');
            }
        }

        // 更新进度文本
        _updateProgressText = (uploadItem, text) => {
            const progressText = uploadItem.find('.upload-list__progress__text');
            if (progressText.length > 0) {
                progressText.text(text);
            }
        }

        // 更新上传进度
        _updateUploadProgress = (uploadItem, file, percentage) => {
            const progressBarInner = uploadItem.find('.upload-list__progress__inner');
            const progressBarText = uploadItem.find('.upload-list__progress__text');

            if (progressBarInner.length > 0) {
                progressBarInner.width(percentage * 100 + '%');
            }

            if (progressBarText.length > 0) {
                const percent = Math.round(percentage * 100);
                progressBarText.text(percent + '%');
            }
        }

        // 更新分片进度显示
        _updateChunkProgressDisplay = (uploadItem, uploadedChunks, totalChunks) => {
            const percentage = uploadedChunks / totalChunks;
            const progressBarInner = uploadItem.find('.upload-list__progress__inner');
            const progressBarText = uploadItem.find('.upload-list__progress__text');

            if (progressBarInner.length > 0) {
                progressBarInner.width(percentage * 100 + '%');
            }

            if (progressBarText.length > 0) {
                const percent = Math.round(percentage * 100);
                progressBarText.text(`${uploadedChunks}/${totalChunks} (${percent}%)`);
            }
        }

        // 更新分片上传进度
        _updateChunkProgress = (file, data) => {
            if (data && data.uploadedChunks !== undefined && data.totalChunks !== undefined) {
                const uploadItem = file._uploadItem;
                if (uploadItem) {
                    this._updateChunkProgressDisplay(uploadItem, data.uploadedChunks, data.totalChunks);
                }
            }
        }

        // 获取文件扩展名
        _getFileExt = (fileName) => {
            const ext = fileName.split('.').pop();
            return ext ? ext.toLowerCase() : '';
        }
    }

    $.fn.initUploader = function (options) {
        return this.each(function () {
            var uploader = new Uploader(this, options);
            $(this).data("uploader", uploader);
        });
    }
})(window, jQuery);