﻿// 封装组件 闭包环境
(function (window, $) {
    // 文件上传相关参数
    const uploadDefaultOptions = {
        // 文件数量限制
        fileNumLimit: 5,
        // 是否多文件
        multiple: false,
        // 添加水印
        water: false,
        // 生成缩略图
        thumbnail: false,
        // 删除文件时是否删除远程服务器文件
        deleteServerFile: true,
        // 文件大小限制
        fileSizeLimit: 200 * 1024 * 1024,    //200 M
        // swf文件路径
        swf: './uploader.swf',
        // 文件类型
        accept: 'jpg,jpge,png,gif,xls,xlsx,doc,docx,ppt,pptx,pdf,zip,rar,7z',
        // 文件接收服务端。
        server: '',
        // 选择文件的按钮。可选。
        // 按钮文字
        btnText: "选择文件",
        // 已上传的文件列表
        fileList: [],
        // 上传文件成功后的回调函数
        onSuccess: null,
        // 上传成功的文件移除后的回调函数
        onRemove: null,
    }

    class Uploader {
        constructor(element, options) {
            // 判断element是否有值
            if (!element) {
                console.error("初始化上传控件需要指定dom元素");
                return;
            }
            // 判断options中server是否有值
            if (!options.server || options.server === '') {
                console.error("初始化上传控件需要指定server参数");
                return;
            }
            // 判断element是否是字符串
            if (typeof element === 'string') {
                this.element = $(element);
            }
            else if (element instanceof HTMLElement) {
                this.element = $(element);
            }
            else if (element instanceof jQuery) {
                this.element = element;
            }
            else {
                console.error("初始化上传控件发生错误:element参数类型错误");
                return;
            }
            // 合并默认参数
            this.options = $.extend({}, uploadDefaultOptions, options);
            // 初始设置已上传文件数量为0
            this.fileNum = 0;
            // 初始化
            this._init();
        }

        _init() {
            // 判断是否有layer组件
            if (window.layer) {
                this.showMsg = layer.msg;
            }
            else {
                this.showMsg = function (msg) {
                    top.alert(msg);
                };
            }
            // 初始化上传控件
            this._initUploader();
        }

        /**
             * 创建进度条
             */
        _createProgressBar = () => {
            let progress = $('<div class="upload-list__progress"></div>');
            let progressBarOuter = $('<div class="upload-list__progress__outer"></div>');
            let progressBarInner = $('<div class="upload-list__progress__inner"></div>');
            let progressBarText = $('<div class="upload-list__progress__text"></div>');
            progressBarText.text('0%');
            progressBarOuter.append(progressBarInner);
            progress.append(progressBarOuter, progressBarText);
            return progress;
        };

        /**
         * 创建上传列表项
         * @param file 待上传文件信息
         * @param isUploaded 是否是已上传完的文件
         * @returns {*|jQuery|HTMLElement}
         * @private
         */
        _createUploadItem = (file, isUploaded = false) => {
            let item = $('<div class="upload-list__item"></div>').attr('id', file.id);
            let itemInfo = $('<div class="upload-list__item-info"></div>');
            let itemStatus = $('<div class="upload-list__item-status"></div>');
            let itemCancel = $('<div class="upload-list__item-close"><img src="/admin/images/delete.png" alt="" /></div>');
            item.append(itemInfo, itemStatus, itemCancel);
            let title = $('<div class="upload-list__title"><span>' + file.name + '</span></div>');
            itemInfo.append(title);
            // 文件上传状态不存在时，创建进度条
            if (!isUploaded) {
                let progress = this._createProgressBar();
                itemInfo.append(progress);
            }
            else {
                this._setUploadItemStatus(item, 1);
            }
            itemStatus.append($('<img src="/admin/images/true.gif" alt="" />'));
            itemCancel.on('click', () => {
                let fileStatus = item.data('status');
                item.addClass('upload-list__item-leave');
                // 判断创建上传列表项时是否是已经上传完的状态
                if (!isUploaded) {
                    // 删除文件
                    this.uploader.removeFile(file);
                }
                if (this.options.deleteServerFile) {
                    // 通知服务器删除文件
                    this._deleteFile(fileStatus);
                }
                this.fileNum--;
                // 延迟删除,让动画执行完
                setTimeout(function () {
                    item.remove();
                }, 500);
                if (typeof this.options.onRemove === 'function') {
                    this.options.onRemove(fileStatus);
                }
            });
            this.uploadList.append(item);
            this.fileNum++;
            return item;
        }

        /**
         * 设置上传状态
         */
        _setUploadItemStatus = (dom, status) => {
            dom.addClass(status === 1 ? 'success' : 'error');
        }

        _initUploader() {
            let queryParams = '';
            if (this.options.water) {
                queryParams += '&IsWater=1';
            }
            if (this.options.thumbnail) {
                queryParams += '&IsThumbnail=1';
            }
            if (queryParams !== '') {
                queryParams = queryParams.substring(1);
                if (this.options.server.includes('?')) {
                    this.options.server = this.options.server + '&' + queryParams;
                }
                else {
                    this.options.server = this.options.server + '?' + queryParams;
                }
            }
            // 创建上传按钮
            let btnDom = $('<div class="upload-btn"></div>').text(this.options.btnText).appendTo(this.element);
            // 创建展示列表
            this.uploadList = $('<div class="upload-list"></div>').appendTo(this.element);
            let uploader = WebUploader.create({
                auto: true, //自动上传
                swf: this.options.swf, //SWF路径
                server: this.options.server, //上传地址
                pick: {
                    id: btnDom,
                    multiple: this.options.multiple
                },
                accept: {
                    extensions: this.options.accept
                },
                compress: false,//不启用压缩, 最大尺寸1600，只
                formData: this.formData,
                fileVal: 'Filedata', //上传域的名称
                fileNumLimit: this.options.fileNumLimit,
                fileSingleSizeLimit: this.options.fileSizeLimit
            });
            var self = this;
            // 当验证不通过时，会以派送错误事件的形式通知
            uploader.on('error', function (type) {
                switch (type) {
                    case 'Q_EXCEED_NUM_LIMIT':
                        self.showMsg(`最多允许上传${self.options.fileNumLimit}个文件`);
                        break;
                    case 'Q_EXCEED_SIZE_LIMIT':
                        self.showMsg("文件总大小超出限制！");
                        break;
                    case 'F_EXCEED_SIZE':
                        self.showMsg("文件大小超出限制！");
                        break;
                    case 'Q_TYPE_DENIED':
                        self.showMsg("禁止上传该类型文件！");
                        break;
                    case 'F_DUPLICATE':
                        self.showMsg("请勿重复上传该文件！");
                        break;
                    default:
                        self.showMsg('错误代码：' + type);
                        break;
                }
            });

            // 当文件被加入队列之前触发。如果此事件handler的返回值为false，则此文件不会被添加进入队列。
            uploader.on('beforeFileQueued', function (file) {
                if (self.options.fileNumLimit) {
                    if (self.fileNum >= self.options.fileNumLimit) {
                        self.showMsg(`最多允许上传${self.options.fileNumLimit}个文件`);
                        return false;
                    }
                }
            });

            //当有文件添加进来的时候
            uploader.on('fileQueued', function (file) {
                // 创建上传的文件列表
                self._createUploadItem(file);
            });

            //文件上传过程中创建进度条实时显示
            uploader.on('uploadProgress', function (file, percentage) {
                let item = self.uploadList.find(`#${file.id}`);
                let progressBarInner = item.find('.upload-list__progress__inner');
                let progressBarText = item.find('.upload-list__progress__text');
                progressBarInner.width(percentage * 100 + '%');
                progressBarText.text(Math.round(percentage * 100) + '%');
            });

            //当文件上传出错时触发
            uploader.on('uploadError', function (file, reason) {
                let item = self.uploadList.find(`#${file.id}`);
                item.fadeOut(500, function () {
                    // 移除dom
                    item.remove();
                    // 从队列中移除
                    uploader.removeFile(file);
                    self.showMsg(file.name + "上传失败，错误代码：" + reason);
                })
            });

            //当文件上传成功时触发
            uploader.on('uploadSuccess', function (file, data) {
                let item = self.uploadList.find(`#${file.id}`);
                self._setUploadItemStatus(item, data.status);
                item.data('status', data);
                // 判断是否有注册回调函数
                if (typeof self.options.onSuccess === 'function') {
                    self.options.onSuccess(data);
                }
            });

            //不管成功或者失败，文件上传完成时触发
            uploader.on('uploadComplete', function (file) {
                let progress = self.uploadList.find(`#${file.id} .upload-list__progress`);
                progress.fadeOut(800, function () {
                    progress.remove();
                });
            });


            this._renderUploadedFile();
            this.uploader = uploader;
        }

        _deleteFile = (data) => {
            if (data && data.path) {
                $.ajax({
                    type: 'post',
                    url: '/ajax/upload_file.ashx?action=deleteFile',
                    data: data,
                    success: function (data) {
                        console.log(data);
                    },
                })
            }
        }

        _renderUploadedFile = () => {
            // 处理已经存在的文件
            if (Array.isArray(this.options.fileList) && this.options.fileList.length > 0) {
                this.options.fileList.forEach((item, index) => {
                    // 创建上传的文件列表
                    let uploadItem = this._createUploadItem({ id: `uploaded_${item.name}_${index}`, name: item.name, path: item.path }, true);
                    uploadItem.data('status', item);
                });
            }
        }

        refreshFileList = (data) => {
            this.options.fileList = data;
            this.uploadList.empty();
            this._renderUploadedFile();
        }
    }

    $.fn.initUploader = function (options) {
        return this.each(function () {
            var uploader = new Uploader(this, options);
            $(this).data("uploader", uploader);
        });
    }
})(window, jQuery);