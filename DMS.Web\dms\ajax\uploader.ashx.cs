﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Web;
using System.Web.SessionState;
using Newtonsoft.Json;
using DMS.BLL;

namespace DMS.Web.dms.ajax
{
    /// <summary>
    /// 分片上传处理器
    /// </summary>
    public class uploader : IHttpHandler, IRequiresSessionState
    {
        #region 私有字段

        private readonly YunEdu.Authority.AdminCommonJC _ac = new YunEdu.Authority.AdminCommonJC();
        private readonly dms_files _bllFiles = new dms_files();
        private readonly dms_directory _bllDirectory = new dms_directory();

        // 分片上传缓存目录
        private readonly string _chunkCacheDir = "/dms/temp/chunks/";
        // 分片状态缓存目录
        private readonly string _statusCacheDir = "/dms/temp/status/";

        #endregion

        #region 主处理方法

        public void ProcessRequest(HttpContext context)
        {
            if (!_ac.User.Identity.IsAuthenticated)
            {
                WriteErrorResponse(context, "用户未登录");
                return;
            }

            context.Response.ContentType = "application/json";
            string action = context.Request.QueryString["action"] ?? context.Request.Form["action"];

            try
            {
                switch (action)
                {
                    case "upload": // 文件分片上传
                        HandleChunkUpload(context);
                        break;
                    case "status": // 文件分片查询
                        HandleStatusQuery(context);
                        break;
                    case "merge": // 文件分片合并
                        HandleMergeChunks(context);
                        break;
                    default:
                        WriteErrorResponse(context, "无效的操作类型");
                        break;
                }
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"处理请求时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 分片上传处理

        /// <summary>
        /// 处理分片上传
        /// </summary>
        private void HandleChunkUpload(HttpContext context)
        {
            var request = context.Request;

            // 获取分片参数
            string fileMD5 = request.Form["fileMD5"];
            string fileName = request.Form["fileName"];
            int chunkIndex = int.Parse(request.Form["chunkIndex"] ?? "0");
            int totalChunks = int.Parse(request.Form["totalChunks"] ?? "1");
            long fileSize = long.Parse(request.Form["fileSize"] ?? "0");
            int directoryId = int.Parse(request.Form["directoryId"] ?? "0");

            if (string.IsNullOrEmpty(fileMD5) || string.IsNullOrEmpty(fileName))
            {
                WriteErrorResponse(context, "缺少必要参数");
                return;
            }

            // 检查文件是否已存在
            var existingFile = _bllFiles.GetModelByMD5(fileMD5);
            if (existingFile != null && existingFile.FileStatus == (int)CommonEnum.dms_FileStatus.Completed)
            {
                WriteSuccessResponse(context, new
                {
                    uploaded = true,
                    fileId = existingFile.Id,
                    filePath = existingFile.FilePath,
                    message = "文件已存在，秒传成功"
                });
                return;
            }

            // 保存分片文件
            if (request.Files.Count > 0)
            {
                var chunkFile = request.Files[0];
                string chunkPath = SaveChunkFile(fileMD5, chunkIndex, chunkFile);

                if (!string.IsNullOrEmpty(chunkPath))
                {
                    // 更新分片状态
                    UpdateChunkStatus(fileMD5, chunkIndex, totalChunks, fileName, fileSize, directoryId);

                    // 检查是否所有分片都已上传完毕
                    var chunkStatus = GetChunkStatus(fileMD5);
                    if (chunkStatus != null && chunkStatus.UploadedChunks.Count == totalChunks)
                    {
                        // 所有分片都已上传完毕，自动合并文件
                        try
                        {
                            string finalFilePath = MergeChunkFiles(fileMD5, chunkStatus);

                            if (!string.IsNullOrEmpty(finalFilePath))
                            {
                                // 保存文件信息到数据库
                                var fileModel = CreateFileModel(chunkStatus, finalFilePath, directoryId);
                                _bllFiles.Add(fileModel);

                                // 清理临时文件和状态
                                CleanupChunkFiles(fileMD5);

                                WriteSuccessResponse(context, new
                                {
                                    uploaded = true,
                                    completed = true,
                                    chunkIndex = chunkIndex,
                                    fileId = fileModel.Id,
                                    filePath = finalFilePath,
                                    message = $"分片 {chunkIndex + 1}/{totalChunks} 上传成功，文件合并完成"
                                });
                            }
                            else
                            {
                                WriteErrorResponse(context, "文件合并失败");
                            }
                        }
                        catch (Exception ex)
                        {
                            WriteErrorResponse(context, $"合并文件时发生错误: {ex.Message}");
                        }
                    }
                    else
                    {
                        // 还有分片未上传完毕，返回当前状态
                        WriteSuccessResponse(context, new
                        {
                            uploaded = true,
                            completed = false,
                            chunkIndex = chunkIndex,
                            uploadedChunks = chunkStatus?.UploadedChunks.Count ?? 0,
                            totalChunks = totalChunks,
                            message = $"分片 {chunkIndex + 1}/{totalChunks} 上传成功"
                        });
                    }
                }
                else
                {
                    WriteErrorResponse(context, "分片保存失败");
                }
            }
            else
            {
                WriteErrorResponse(context, "未找到上传文件");
            }
        }

        #endregion

        #region 状态查询处理

        /// <summary>
        /// 处理状态查询
        /// </summary>
        private void HandleStatusQuery(HttpContext context)
        {
            string fileMD5 = context.Request.QueryString["fileMD5"];

            if (string.IsNullOrEmpty(fileMD5))
            {
                WriteErrorResponse(context, "缺少文件MD5参数");
                return;
            }

            // 1. 先查询数据库是否已有完整文件
            var existingFile = _bllFiles.GetModelByMD5(fileMD5);
            if (existingFile != null && existingFile.FileStatus == (int)CommonEnum.dms_FileStatus.Completed)
            {
                WriteSuccessResponse(context, new
                {
                    exists = true,
                    completed = true,
                    fileId = existingFile.Id,
                    filePath = existingFile.FilePath,
                    uploadedChunks = new int[0]
                });
                return;
            }

            // 2. 查询分片上传状态
            var chunkStatus = GetChunkStatus(fileMD5);
            if (chunkStatus != null)
            {
                WriteSuccessResponse(context, new
                {
                    exists = true,
                    completed = false,
                    totalChunks = chunkStatus.TotalChunks,
                    uploadedChunks = chunkStatus.UploadedChunks.ToArray(),
                    fileName = chunkStatus.FileName
                });
            }
            else
            {
                WriteSuccessResponse(context, new
                {
                    exists = false,
                    completed = false,
                    uploadedChunks = new int[0]
                });
            }
        }

        #endregion

        #region 分片合并处理

        /// <summary>
        /// 处理分片合并
        /// </summary>
        private void HandleMergeChunks(HttpContext context)
        {
            string fileMD5 = context.Request.Form["fileMD5"];
            int directoryId = int.Parse(context.Request.Form["directoryId"] ?? "0");

            if (string.IsNullOrEmpty(fileMD5))
            {
                WriteErrorResponse(context, "缺少文件MD5参数");
                return;
            }

            var chunkStatus = GetChunkStatus(fileMD5);
            if (chunkStatus == null)
            {
                WriteErrorResponse(context, "未找到分片状态信息");
                return;
            }

            // 检查所有分片是否都已上传
            if (chunkStatus.UploadedChunks.Count != chunkStatus.TotalChunks)
            {
                WriteErrorResponse(context, "分片上传未完成");
                return;
            }

            try
            {
                // 合并分片文件
                string finalFilePath = MergeChunkFiles(fileMD5, chunkStatus);

                if (!string.IsNullOrEmpty(finalFilePath))
                {
                    // 保存文件信息到数据库
                    var fileModel = CreateFileModel(chunkStatus, finalFilePath, directoryId);
                    _bllFiles.Add(fileModel);

                    // 清理临时文件和状态
                    CleanupChunkFiles(fileMD5);

                    WriteSuccessResponse(context, new
                    {
                        merged = true,
                        fileId = fileModel.Id,
                        filePath = finalFilePath,
                        message = "文件合并成功"
                    });
                }
                else
                {
                    WriteErrorResponse(context, "文件合并失败");
                }
            }
            catch (Exception ex)
            {
                WriteErrorResponse(context, $"合并文件时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 保存分片文件
        /// </summary>
        private string SaveChunkFile(string fileMD5, int chunkIndex, HttpPostedFile chunkFile)
        {
            try
            {
                string chunkDir = HttpContext.Current.Server.MapPath(_chunkCacheDir + fileMD5);
                if (!Directory.Exists(chunkDir))
                {
                    Directory.CreateDirectory(chunkDir);
                }

                string chunkPath = Path.Combine(chunkDir, $"chunk_{chunkIndex}");
                chunkFile.SaveAs(chunkPath);

                return chunkPath;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 更新分片状态
        /// </summary>
        private void UpdateChunkStatus(string fileMD5, int chunkIndex, int totalChunks, string fileName, long fileSize, int directoryId)
        {
            var status = GetChunkStatus(fileMD5) ?? new ChunkUploadStatus
            {
                FileMD5 = fileMD5,
                FileName = fileName,
                TotalChunks = totalChunks,
                FileSize = fileSize,
                DirectoryId = directoryId,
                UploadedChunks = new List<int>(),
                CreateTime = DateTime.Now
            };

            if (!status.UploadedChunks.Contains(chunkIndex))
            {
                status.UploadedChunks.Add(chunkIndex);
            }

            SaveChunkStatus(fileMD5, status);
        }

        /// <summary>
        /// 获取分片状态
        /// </summary>
        private ChunkUploadStatus GetChunkStatus(string fileMD5)
        {
            try
            {
                string statusFile = HttpContext.Current.Server.MapPath(_statusCacheDir + fileMD5 + ".json");
                if (File.Exists(statusFile))
                {
                    string json = File.ReadAllText(statusFile);
                    return JsonConvert.DeserializeObject<ChunkUploadStatus>(json);
                }
            }
            catch
            {
                // 忽略异常
            }
            return null;
        }

        /// <summary>
        /// 保存分片状态
        /// </summary>
        private void SaveChunkStatus(string fileMD5, ChunkUploadStatus status)
        {
            try
            {
                string statusDir = HttpContext.Current.Server.MapPath(_statusCacheDir);
                if (!Directory.Exists(statusDir))
                {
                    Directory.CreateDirectory(statusDir);
                }

                string statusFile = Path.Combine(statusDir, fileMD5 + ".json");
                string json = JsonConvert.SerializeObject(status);
                File.WriteAllText(statusFile, json);
            }
            catch
            {
                // 忽略异常
            }
        }

        /// <summary>
        /// 写入成功响应
        /// </summary>
        private void WriteSuccessResponse(HttpContext context, object data)
        {
            var response = new { success = true, data = data };
            context.Response.Write(JsonConvert.SerializeObject(response));
        }

        /// <summary>
        /// 写入错误响应
        /// </summary>
        private void WriteErrorResponse(HttpContext context, string message)
        {
            var response = new { success = false, message = message };
            context.Response.Write(JsonConvert.SerializeObject(response));
        }

        /// <summary>
        /// 合并分片文件
        /// </summary>
        private string MergeChunkFiles(string fileMD5, ChunkUploadStatus chunkStatus)
        {
            try
            {
                // 获取FTP配置
                var directory = _bllDirectory.GetModel(chunkStatus.DirectoryId);
                if (directory == null) return null;

                // 生成最终文件路径
                string fileExtension = Path.GetExtension(chunkStatus.FileName);
                string finalFileName = $"{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid():N}{fileExtension}";
                string relativePath = $"{directory.ColumnId}/{finalFileName}";
                // 本地文件合并
                return MergeChunksToLocal(fileMD5, chunkStatus, relativePath);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 合并分片到本地文件
        /// </summary>
        private string MergeChunksToLocal(string fileMD5, ChunkUploadStatus chunkStatus, string relativePath)
        {
            try
            {
                string chunkDir = HttpContext.Current.Server.MapPath(_chunkCacheDir + fileMD5);
                string finalPath = HttpContext.Current.Server.MapPath($"/dms/doc/{relativePath}");

                // 确保目标目录存在
                string targetDir = Path.GetDirectoryName(finalPath);
                if (!Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                using (var finalStream = new FileStream(finalPath, FileMode.Create))
                {
                    for (int i = 0; i < chunkStatus.TotalChunks; i++)
                    {
                        string chunkPath = Path.Combine(chunkDir, $"chunk_{i}");
                        if (File.Exists(chunkPath))
                        {
                            using (var chunkStream = new FileStream(chunkPath, FileMode.Open))
                            {
                                chunkStream.CopyTo(finalStream);
                            }
                        }
                    }
                }

                return $"/dms/doc/{relativePath}";
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建文件模型
        /// </summary>
        private DMS.Model.dms_files CreateFileModel(ChunkUploadStatus chunkStatus, string filePath, int directoryId)
        {
            var directory = _bllDirectory.GetModel(directoryId);

            return new DMS.Model.dms_files
            {
                Id = Guid.NewGuid(),
                FileName = Path.GetFileNameWithoutExtension(chunkStatus.FileName),
                FileFormat = Path.GetExtension(chunkStatus.FileName).TrimStart('.'),
                FileType = GetFileType(Path.GetExtension(chunkStatus.FileName)),
                FilePath = filePath,
                FileSize = (int)(chunkStatus.FileSize / 1024), // 转换为KB
                FileMD5 = chunkStatus.FileMD5,
                FileStatus = (int)CommonEnum.dms_FileStatus.Completed,
                DirectoryId = directoryId,
                DirectoryPath = directory?.Path ?? "",
                ColumnId = directory?.ColumnId ?? _ac.modelAreaUser.ColumnID,
                ColumnPath = directory?.ColumnPath ?? _ac.modelAreaUser.ColumnPath,
                Creator = new Guid(_ac.UserId),
                CreateTime = DateTime.Now,
                IsOpen = 0,
                Memo = ""
            };
        }

        /// <summary>
        /// 清理分片文件和状态
        /// </summary>
        private void CleanupChunkFiles(string fileMD5)
        {
            try
            {
                // 清理分片文件
                string chunkDir = HttpContext.Current.Server.MapPath(_chunkCacheDir + fileMD5);
                if (Directory.Exists(chunkDir))
                {
                    Directory.Delete(chunkDir, true);
                }

                // 清理状态文件
                string statusFile = HttpContext.Current.Server.MapPath(_statusCacheDir + fileMD5 + ".json");
                if (File.Exists(statusFile))
                {
                    File.Delete(statusFile);
                }
            }
            catch
            {
                // 忽略清理异常
            }
        }

        #endregion


        #region 文件类型识别

        /// <summary>
        /// 根据文件扩展名获取文件类型
        /// </summary>
        private int GetFileType(string extension)
        {
            if (string.IsNullOrEmpty(extension))
                return (int)CommonEnum.dms_FileType.Others;

            extension = extension.ToLower().TrimStart('.');

            switch (extension)
            {
                case "txt":
                    return (int)CommonEnum.dms_FileType.Txt;
                case "doc":
                case "docx":
                    return (int)CommonEnum.dms_FileType.Word;
                case "xls":
                case "xlsx":
                    return (int)CommonEnum.dms_FileType.Excel;
                case "ppt":
                case "pptx":
                    return (int)CommonEnum.dms_FileType.Ppt;
                case "jpg":
                case "jpeg":
                case "png":
                case "gif":
                case "bmp":
                case "pdf":
                case "tiff":
                case "swf":
                    return (int)CommonEnum.dms_FileType.Image;
                case "avi":
                case "rmvb":
                case "mp4":
                case "mid":
                case "mov":
                case "wmv":
                case "flv":
                    return (int)CommonEnum.dms_FileType.Video;
                case "wma":
                case "mp3":
                case "wav":
                case "aac":
                    return (int)CommonEnum.dms_FileType.Voice;
                case "rar":
                case "zip":
                case "7z":
                    return (int)CommonEnum.dms_FileType.Rar;
                default:
                    return (int)CommonEnum.dms_FileType.Others;
            }
        }

        #endregion

        public bool IsReusable => false;
    }

    /// <summary>
    /// 分片上传状态类
    /// </summary>
    public class ChunkUploadStatus
    {
        public string FileMD5 { get; set; }
        public string FileName { get; set; }
        public int TotalChunks { get; set; }
        public long FileSize { get; set; }
        public int DirectoryId { get; set; }
        public List<int> UploadedChunks { get; set; }
        public DateTime CreateTime { get; set; }
    }
}