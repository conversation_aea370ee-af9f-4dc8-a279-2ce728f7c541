# 分片上传API文档

## 概述
本系统提供了完整的分片上传功能，支持大文件的断点续传、秒传和自动合并。当所有分片上传完成后，系统会自动合并文件并保存到数据库。

## API接口

### 1. 分片上传接口

**接口地址：** `POST /dms/ajax/uploader.ashx?action=upload`

**请求参数：**
- `fileMD5` (string): 文件MD5值，用于唯一标识文件
- `fileName` (string): 原始文件名
- `chunkIndex` (int): 当前分片索引，从0开始
- `totalChunks` (int): 总分片数量
- `fileSize` (long): 文件总大小（字节）
- `directoryId` (int): 目标目录ID
- `file` (file): 分片文件数据

**响应格式：**

**普通分片上传成功：**
```json
{
  "success": true,
  "data": {
    "uploaded": true,
    "completed": false,
    "chunkIndex": 2,
    "uploadedChunks": 3,
    "totalChunks": 10,
    "message": "分片 3/10 上传成功"
  }
}
```

**最后一个分片上传成功（自动合并）：**
```json
{
  "success": true,
  "data": {
    "uploaded": true,
    "completed": true,
    "chunkIndex": 9,
    "fileId": "12345678-1234-1234-1234-123456789abc",
    "filePath": "/dms/doc/1001/20241212143022_abc123.pdf",
    "message": "分片 10/10 上传成功，文件合并完成"
  }
}
```

**文件已存在（秒传）：**
```json
{
  "success": true,
  "data": {
    "uploaded": true,
    "fileId": "12345678-1234-1234-1234-123456789abc",
    "filePath": "/dms/doc/1001/existing_file.pdf",
    "message": "文件已存在，秒传成功"
  }
}
```

### 2. 状态查询接口

**接口地址：** `GET /dms/ajax/uploader.ashx?action=status&fileMD5={md5}`

**请求参数：**
- `fileMD5` (string): 文件MD5值

**响应格式：**

**文件已完成：**
```json
{
  "success": true,
  "data": {
    "exists": true,
    "completed": true,
    "fileId": "12345678-1234-1234-1234-123456789abc",
    "filePath": "/dms/doc/1001/completed_file.pdf",
    "uploadedChunks": []
  }
}
```

**分片上传中：**
```json
{
  "success": true,
  "data": {
    "exists": true,
    "completed": false,
    "totalChunks": 10,
    "uploadedChunks": [0, 1, 2, 3, 4],
    "fileName": "large_file.pdf"
  }
}
```

**文件不存在：**
```json
{
  "success": true,
  "data": {
    "exists": false,
    "completed": false,
    "uploadedChunks": []
  }
}
```

### 3. 手动合并接口（可选）

**接口地址：** `POST /dms/ajax/uploader.ashx?action=merge`

**说明：** 通常不需要调用此接口，因为系统会在所有分片上传完成后自动合并。此接口主要用于异常情况下的手动合并。

**请求参数：**
- `fileMD5` (string): 文件MD5值
- `directoryId` (int): 目标目录ID

## 使用流程

### 标准上传流程

1. **计算文件MD5**
   - 前端计算完整文件的MD5值

2. **查询文件状态**
   - 调用状态查询接口检查文件是否已存在
   - 如果已存在且完成，则实现秒传
   - 如果存在未完成的分片，获取已上传分片列表

3. **分片上传**
   - 将文件分割成固定大小的分片（建议1-5MB）
   - 逐个上传未完成的分片
   - 系统会在最后一个分片上传完成后自动合并文件

4. **完成处理**
   - 当收到 `completed: true` 的响应时，表示文件上传和合并已完成
   - 可以使用返回的 `fileId` 和 `filePath` 进行后续操作

### 断点续传流程

1. **检测中断**
   - 上传过程中如果发生网络中断或其他异常

2. **恢复上传**
   - 重新调用状态查询接口获取已上传分片列表
   - 只上传未完成的分片
   - 系统会自动处理重复分片

## 技术特性

### 自动合并机制
- **触发条件：** 当最后一个分片上传完成时自动触发
- **合并过程：** 按分片顺序合并为完整文件
- **存储支持：** 自动识别本地存储或FTP存储
- **数据库保存：** 合并完成后自动保存文件信息到dms_files表
- **清理机制：** 合并完成后自动清理临时分片文件和状态文件

### 存储支持
- **本地存储：** 直接保存到服务器本地文件系统
- **FTP存储：** 自动上传到配置的FTP服务器
- **智能识别：** 根据目录配置自动选择存储方式

### 状态管理
- **JSON缓存：** 分片状态保存在JSON文件中，支持服务重启后恢复
- **内存缓存：** 运行时状态查询，提高性能
- **数据库集成：** 完成后的文件信息保存在数据库中

### 错误处理
- **容错机制：** 单个分片失败不影响整体上传
- **自动重试：** 支持分片级别的重试
- **状态恢复：** 异常中断后可恢复到之前的上传状态

## 前端集成示例

```javascript
// 使用WebUploader Helper进行分片上传
$("#uploadContainer").InitUploader({
    sendurl: "ajax/uploader.ashx",
    directoryId: 1,
    chunked: true,
    chunkSize: 2,
    callback: function(data) {
        if (data.status == '1') {
            console.log('文件上传完成！', data.fileId);
        }
    }
});

// 或者直接使用WebUploader API
var uploader = WebUploader.create({
    server: 'ajax/uploader.ashx?action=upload',
    chunked: true,
    chunkSize: 2 * 1024 * 1024,
    formData: {
        directoryId: 1
    }
});

// 文件添加后自动计算MD5并上传
uploader.on('fileQueued', function(file) {
    WebUploader.md5File(file, 0, file.size).then(function(md5) {
        uploader.options.formData.fileMD5 = md5;
        uploader.options.formData.fileName = file.name;
        uploader.options.formData.fileSize = file.size;
        uploader.upload(file);
    });
});
```

## 注意事项

1. **分片大小建议：** 1-5MB，过小会增加请求次数，过大可能导致超时
2. **MD5计算：** 必须是完整文件的MD5，用于唯一标识和秒传功能
3. **目录权限：** 确保用户对目标目录有写入权限
4. **临时目录：** 系统会在 `/dms/temp/` 下创建临时目录，需要确保有足够空间
5. **自动清理：** 合并完成后会自动清理临时文件，无需手动处理
