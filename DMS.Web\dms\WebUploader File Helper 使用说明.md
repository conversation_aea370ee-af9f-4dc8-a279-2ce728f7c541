# WebUploader File Helper 使用说明

## 概述
`webuploader.helper.file.js` 是一个基于类的WebUploader文件上传组件，现已升级支持分片上传和断点续传功能。它提供了更现代的API设计和更好的用户体验。

## 主要特性

### 1. **现代化设计**
- **ES6类语法**：使用现代JavaScript类语法编写
- **链式调用**：支持jQuery风格的链式调用
- **事件驱动**：完整的事件回调机制

### 2. **分片上传功能**
- **自动分片**：大文件自动分割成小块上传
- **智能断点续传**：自动检测已上传分片，跳过重复上传
- **秒传功能**：相同文件MD5检测，避免重复上传
- **自动合并**：所有分片上传完成后自动合并
- **进度显示**：实时显示上传进度和分片状态

### 3. **用户界面**
- **可视化进度条**：美观的进度条显示
- **文件列表管理**：完整的文件列表展示和管理
- **拖拽删除**：支持文件删除操作
- **状态指示**：清晰的成功/失败状态显示

## 配置参数

### 基本配置
```javascript
const options = {
    // 文件数量限制
    fileNumLimit: 5,
    // 是否多文件上传
    multiple: false,
    // 添加水印
    water: false,
    // 生成缩略图
    thumbnail: false,
    // 删除文件时是否删除远程服务器文件
    deleteServerFile: true,
    // 文件大小限制（字节）
    fileSizeLimit: 200 * 1024 * 1024,    // 200MB
    // SWF文件路径
    swf: './uploader.swf',
    // 允许的文件类型
    accept: 'jpg,jpeg,png,gif,xls,xlsx,doc,docx,ppt,pptx,pdf,zip,rar,7z',
    // 文件接收服务端
    server: 'ajax/uploader.ashx',
    // 按钮文字
    btnText: "选择文件",
    // 已上传的文件列表
    fileList: [],
    // 上传成功回调
    onSuccess: null,
    // 文件移除回调
    onRemove: null
};
```

### 分片上传配置
```javascript
const chunkOptions = {
    // 是否启用分片上传，默认启用
    chunked: true,
    // 分片大小(MB)，默认2MB
    chunkSize: 2,
    // 目标目录ID
    directoryId: 1
};
```

## 使用方法

### 1. **基本初始化**
```html
<div id="uploader"></div>

<script>
$('#uploader').initUploader({
    server: 'ajax/uploader.ashx',
    directoryId: 1,
    onSuccess: function(data) {
        console.log('上传成功:', data);
    }
});
</script>
```

### 2. **分片上传配置**
```javascript
$('#uploader').initUploader({
    server: 'ajax/uploader.ashx',
    directoryId: 1,
    chunked: true,          // 启用分片上传
    chunkSize: 5,           // 5MB分片
    fileSizeLimit: 1024 * 1024 * 1024, // 1GB限制
    accept: '*',            // 允许所有文件类型
    multiple: true,         // 多文件上传
    fileNumLimit: 10,       // 最多10个文件
    onSuccess: function(data) {
        console.log('文件上传成功:', data);
    },
    onRemove: function(data) {
        console.log('文件已移除:', data);
    }
});
```

### 3. **获取上传器实例**
```javascript
// 初始化后获取实例
const uploader = $('#uploader').data('uploader');

// 刷新文件列表
uploader.refreshFileList([
    { name: "文件1.pdf", path: "/path/to/file1.pdf" },
    { name: "文件2.docx", path: "/path/to/file2.docx" }
]);

// 获取当前文件数量
console.log('当前文件数量:', uploader.fileNum);
```

## API参考

### 初始化方法
```javascript
$('#element').initUploader(options)
```

### 实例方法
```javascript
// 刷新文件列表
uploader.refreshFileList(fileArray)

// 获取文件数量
uploader.fileNum
```

### 回调函数
```javascript
{
    // 上传成功回调
    onSuccess: function(data) {
        // data 包含文件信息
        console.log(data.path);     // 文件路径
        console.log(data.fileId);   // 文件ID
        console.log(data.name);     // 文件名
    },
    
    // 文件移除回调
    onRemove: function(data) {
        // data 包含被移除的文件信息
        console.log('文件已移除:', data.name);
    }
}
```

## 样式定制

### CSS类名
```css
/* 上传按钮 */
.upload-btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border-radius: 5px;
    cursor: pointer;
}

/* 文件列表容器 */
.upload-list {
    border: 1px solid #ddd;
    border-radius: 5px;
    min-height: 100px;
    padding: 10px;
}

/* 文件列表项 */
.upload-list__item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    margin-bottom: 10px;
}

/* 成功状态 */
.upload-list__item.success {
    border-color: #28a745;
    background-color: #d4edda;
}

/* 失败状态 */
.upload-list__item.error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

/* 进度条 */
.upload-list__progress__outer {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.upload-list__progress__inner {
    height: 100%;
    background-color: #28a745;
    width: 0%;
    transition: width 0.3s ease;
}
```

## 断点续传工作流程

### 1. **文件选择**
- 用户选择文件
- 创建上传项UI
- 开始MD5计算

### 2. **状态检查**
- 计算文件MD5值
- 查询服务器状态
- 检测已上传分片

### 3. **智能上传**
- 跳过已上传分片
- 只上传缺失部分
- 实时更新进度

### 4. **自动合并**
- 所有分片完成后自动合并
- 更新UI状态
- 触发成功回调

## 错误处理

### 常见错误类型
```javascript
// 文件数量超限
'Q_EXCEED_NUM_LIMIT'

// 文件大小超限
'F_EXCEED_SIZE'

// 文件类型不支持
'Q_TYPE_DENIED'

// 重复文件
'F_DUPLICATE'
```

### 错误处理示例
```javascript
$('#uploader').initUploader({
    server: 'ajax/uploader.ashx',
    directoryId: 1,
    onSuccess: function(data) {
        if (data.status === 1) {
            console.log('上传成功');
        } else {
            console.error('上传失败:', data.message);
        }
    }
});
```

## 与原版本的区别

### webuploader.helper.js vs webuploader.helper.file.js

| 特性 | helper.js | helper.file.js |
|------|-----------|----------------|
| 编程风格 | jQuery插件 | ES6类 |
| UI设计 | 简单进度条 | 完整文件列表 |
| 文件管理 | 基础功能 | 高级管理 |
| 事件系统 | 回调函数 | 完整事件 |
| 样式定制 | 有限 | 完全可定制 |
| 断点续传 | ✓ | ✓ |
| 分片上传 | ✓ | ✓ |

## 最佳实践

### 1. **性能优化**
```javascript
// 大文件使用较大分片
{
    chunkSize: 10,  // 10MB分片，适合大文件
    fileSizeLimit: 5 * 1024 * 1024 * 1024  // 5GB限制
}

// 小文件使用较小分片
{
    chunkSize: 1,   // 1MB分片，适合小文件
    fileSizeLimit: 100 * 1024 * 1024  // 100MB限制
}
```

### 2. **用户体验**
```javascript
{
    btnText: "点击选择文件或拖拽到此处",
    onSuccess: function(data) {
        // 显示成功消息
        alert('文件上传成功！');
    },
    onRemove: function(data) {
        // 确认删除
        if (confirm('确定要删除这个文件吗？')) {
            console.log('文件已删除');
        }
    }
}
```

### 3. **错误处理**
```javascript
{
    onSuccess: function(data) {
        try {
            if (data.status === 1) {
                // 处理成功逻辑
            } else {
                throw new Error(data.message || '上传失败');
            }
        } catch (error) {
            console.error('处理上传结果时出错:', error);
        }
    }
}
```

## 示例代码

完整的示例代码请参考：
- `webuploader_file_chunk_demo.html` - 完整演示页面
- `ajax/uploader.ashx.cs` - 服务端处理代码

这个升级版本提供了更现代、更强大的文件上传体验，特别适合需要处理大文件和要求高用户体验的应用场景。
