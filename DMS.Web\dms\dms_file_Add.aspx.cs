﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.Common.DEncrypt;

namespace DMS.Web.dms
{
    public partial class dms_file_Add : System.Web.UI.Page
    {
        public string ImgName = "";
        public string IsAdd = "1";
        Guid Id = Guid.Empty;
        int directoryId = 0;
        Model.dms_files modelfiles = new Model.dms_files();
        BLL.dms_files bllfiles = new BLL.dms_files();
        YunEdu.Authority.AdminCommonJC _ac = new YunEdu.Authority.AdminCommonJC();
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(Request.QueryString["Id"])) Guid.TryParse(Request.QueryString["Id"], out Id);
            if (!string.IsNullOrEmpty(Request.QueryString["directoryId"])) int.TryParse(Request.QueryString["directoryId"], out directoryId);
            if (!IsPostBack)
            {
                hidSchool.Value = _ac.modelAreaUser.ColumnID.ToString();
                hidDirectoryId.Value = directoryId.ToString();
                string _domain = DESEncrypt.Encrypt(Request.Url.Authority.ToLower(), CodeTable.EncryptKeySys);
                spanFileType.InnerText = bllfiles.GetFileTypeLimit(_ac.modelAreaUser.ColumnID, _domain);
                hidFileType.Value = spanFileType.InnerText.Replace("*.", "").Replace("，","");
                hidFileSize.Value = bllfiles.GetFileSizeLimit(_ac.modelAreaUser.ColumnID, _domain);
                show();
            }
        }
        private void show()
        {
            modelfiles = bllfiles.GetModel(Id);
            //编辑
            if (Id != Guid.Empty && modelfiles != null)
            {
                IsAdd = "0";
                this.txtTitle.Text = modelfiles.FileName;
                this.txtContents.Text = modelfiles.Memo;
                ////文件展示（size大小KB转换为B）
                StringBuilder sbGrow = new StringBuilder();
                if (!string.IsNullOrEmpty(modelfiles.FilePath))
                {
                    sbGrow.Append("{\"path\":\"" + modelfiles.FilePath + "\",\"thumb\":\"" + modelfiles.FilePath + "\",\"size\":\"" + (modelfiles.FileSize * 1024) + "\",\"ext\":\"" + modelfiles.FilePath.ToString().Substring(modelfiles.FilePath.LastIndexOf('.') + 1) + "\",\"name\":\"" + Regex.Split(modelfiles.FilePath, "//", RegexOptions.IgnoreCase)[0] + "\"}");
                    ImgName = Regex.Split(modelfiles.FilePath, "//", RegexOptions.IgnoreCase)[0] + "|";
                    this.hfldFileJSON.Value = sbGrow.ToString();
                }

                if (modelfiles.IsOpen == 1)
                {
                    this.chkIsOpen.Checked = true;
                }
                else
                {
                    this.chkIsOpen.Checked = false;
                }
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            if (hidUploadType.Value == "0")
            {
                //获取文件所在目录信息
                Model.dms_directory modelDirectory = new BLL.dms_directory().GetModel(directoryId);
                if (modelDirectory != null)
                {
                    Guid UserId = Guid.Parse(_ac.UserId);
                    //判断用户是否有添加权限
                    Model.dms_auth modelAuth = new BLL.dms_auth().GetModel(UserId, modelDirectory.Id);
                    if ((modelAuth != null && !string.IsNullOrEmpty(modelAuth.Auth) && modelAuth.Auth.IndexOf("1") > -1) ||_ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
                    {
                        //编辑
                        if (Id != Guid.Empty)
                        {
                            modelfiles = bllfiles.GetModel(Id);
                        }
                        else
                        {
                            modelfiles = new Model.dms_files();
                            modelfiles.DirectoryId = modelDirectory.Id;
                            modelfiles.DirectoryPath = modelDirectory.Path;
                            modelfiles.ColumnId = _ac.modelAreaUser.ColumnID;
                            modelfiles.ColumnPath = _ac.modelAreaUser.ColumnPath;
                        }
                        modelfiles.FileName = this.txtTitle.Text;
                        modelfiles.Memo = Regex.Replace(txtContents.Text, @"<(.[^>]*)>", string.Empty, RegexOptions.IgnoreCase);
                        List<WorksFileName> fileList = new System.Web.Script.Serialization.JavaScriptSerializer().Deserialize<List<WorksFileName>>(this.hfldFileJSON.Value);
                        if (fileList.Count > 0)
                        {
                            foreach (WorksFileName list in fileList)
                            {
                                modelfiles.FilePath = list.Path;
                                modelfiles.FileSize = int.Parse(Math.Ceiling(Convert.ToDecimal(list.Size / 1024)).ToString());
                                modelfiles.FileFormat = list.Format;
                                modelfiles.FileType = bllfiles.getFileType(list.Format);
                            }
                        }
                        modelfiles.CreateTime = DateTime.Now;
                        modelfiles.Creator = UserId;
                        if (chkIsOpen.Checked)
                        {
                            modelfiles.IsOpen = 1;
                        }
                        else
                        {
                            modelfiles.IsOpen = 0;
                        }

                        //编辑
                        if (Id != Guid.Empty)
                        {
                            if (bllfiles.Update(modelfiles))
                            {
                                YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('true','编辑成功！',1)");
                            }
                            else
                            {
                                YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','编辑失败！',0)");
                            }
                        }
                        else
                        {
                            if (bllfiles.Add(modelfiles))
                            {
                                YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('true','添加成功！',1)");
                            }
                            else
                            {
                                YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','添加失败！',0)");
                            }
                        }
                    }
                    else
                    {
                        YunEdu.Common.MessageBox.ResponseScript(this, "showMessage('false','您当前没有添加文件权限，请联系管理员！！',0)");
                    }
                }
            }
        }
        public class WorksFileName
        {
            private string _name;
            /// <summary>
            /// 名称
            /// </summary>
            public string Name
            {
                get
                {
                    return _name;
                }
                set
                {
                    _name = value;
                }
            }

            private string _path;
            /// <summary>
            /// 路径
            /// </summary>
            public string Path
            {
                get
                {
                    return _path;
                }
                set
                {
                    _path = value;
                }
            }
            private string _format;
            /// <summary>
            /// 文件格式
            /// </summary>
            public string Format
            {
                get
                {
                    return _format;
                }
                set
                {
                    _format = value;
                }
            }
            private int _size;
            /// <summary>
            /// 文件大小
            /// </summary>
            public int Size
            {
                get
                {
                    return _size;
                }
                set
                {
                    _size = value;
                }
            }
        }
    }
}