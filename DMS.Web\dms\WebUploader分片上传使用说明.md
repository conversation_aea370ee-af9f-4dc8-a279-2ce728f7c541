# WebUploader分片上传使用说明

## 概述
基于原有的 `webuploader.helper.js` 进行了升级，新增了分片上传功能，支持大文件的断点续传、秒传和自动合并。保持了原有API的兼容性，同时提供了更强大的上传能力。

## 主要特性

### 1. **向后兼容**
- 保持原有API接口不变
- 支持传统上传和分片上传两种模式
- 现有代码无需修改即可使用

### 2. **分片上传功能**
- **自动分片**：大文件自动分割成小块上传
- **断点续传**：网络中断后可继续上传
- **秒传功能**：相同文件MD5检测，避免重复上传
- **自动合并**：所有分片上传完成后自动合并
- **进度显示**：实时显示上传进度

### 3. **智能存储**
- **本地存储**：支持服务器本地文件系统
- **FTP存储**：支持FTP服务器存储
- **自动识别**：根据配置自动选择存储方式

## 配置参数

### 新增配置项
```javascript
{
    chunked: true,        // 是否启用分片上传，默认true
    chunkSize: 2,         // 分片大小(MB)，默认2MB
    directoryId: 1,       // 目标目录ID，必填
}
```

### 完整配置示例
```javascript
$("#uploadContainer").InitUploader({
    sendurl: "ajax/uploader.ashx",           // 上传接口地址
    swf: "js/Uploader.swf",                  // Flash文件路径
    filetypes: "jpg,jpeg,png,gif,pdf,doc",  // 允许的文件类型
    filesize: 102400,                        // 文件大小限制(KB)
    multiple: false,                         // 是否多文件上传
    chunked: true,                           // 启用分片上传
    chunkSize: 2,                           // 分片大小(MB)
    directoryId: 1,                         // 目标目录ID
    fileNumLimit: 5,                        // 文件数量限制
    water: false,                           // 是否加水印
    thumbnail: false,                       // 是否生成缩略图
    callback: function(data) {              // 上传完成回调
        console.log("上传完成:", data);
    }
});
```

## 使用方法

### 1. **基本用法（保持原有方式）**
```html
<div id="uploadContainer">
    <input type="hidden" class="upload-path" value="" />
    <a href="javascript:void(0);" class="upload-btn">选择文件</a>
    <div class="photo-list">
        <ul></ul>
    </div>
</div>

<script>
$("#uploadContainer").InitUploader({
    sendurl: "ajax/uploader.ashx",
    directoryId: 1  // 新增必填参数
});
</script>
```

### 2. **分片上传配置**
```javascript
// 启用分片上传（默认）
$("#uploadContainer").InitUploader({
    sendurl: "ajax/uploader.ashx",
    directoryId: 1,
    chunked: true,      // 启用分片
    chunkSize: 5        // 5MB分片
});

// 禁用分片上传（使用传统方式）
$("#uploadContainer").InitUploader({
    sendurl: "ajax/uploader.ashx",
    directoryId: 1,
    chunked: false      // 禁用分片
});
```

### 3. **多文件上传**
```javascript
$("#multiUploadContainer").InitUploader({
    sendurl: "ajax/uploader.ashx",
    directoryId: 1,
    multiple: true,
    fileNumLimit: 10,
    chunked: true,
    chunkSize: 2
});
```

## 依赖文件

### 必需的JavaScript库
```html
<!-- jQuery -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

<!-- SparkMD5 (用于计算文件MD5) -->
<script src="https://cdn.jsdelivr.net/npm/spark-md5@3.0.2/spark-md5.min.js"></script>

<!-- WebUploader -->
<script src="js/webuploader.min.js"></script>

<!-- 升级后的Helper -->
<script src="js/webuploader.helper.js"></script>
```

### CSS样式
```html
<link rel="stylesheet" type="text/css" href="css/webuploader.css" />
```

## 回调函数

### 上传成功回调
```javascript
callback: function(data) {
    if (data.status == '1') {
        // 上传成功
        console.log("文件ID:", data.fileId);
        console.log("文件路径:", data.path);
        console.log("文件MD5:", data.md5);
    } else {
        // 上传失败
        console.log("错误信息:", data.msg);
    }
}
```

### 回调数据格式
```javascript
// 成功时
{
    status: '1',
    path: '/dms/doc/1001/20241212143022_abc123.pdf',
    thumb: '/dms/doc/1001/20241212143022_abc123.pdf',
    size: 1024000,
    ext: 'pdf',
    name: 'document.pdf',
    md5: 'abc123def456...',
    fileId: '12345678-1234-1234-1234-123456789abc'
}

// 失败时
{
    status: '0',
    msg: '错误信息'
}
```

## 进度显示

### 自动进度条
系统会自动创建进度条显示上传状态：
- **计算MD5**：显示"正在计算文件MD5..."
- **检查状态**：显示"检查文件状态..."
- **秒传**：显示"文件已存在，秒传成功"
- **上传中**：显示"上传中：3/10"（已上传分片/总分片）
- **完成**：显示"上传成功：文件名"

### 自定义进度处理
```javascript
// 可以通过CSS自定义进度条样式
.upload-progress {
    /* 进度条容器样式 */
}

.upload-progress .txt {
    /* 进度文本样式 */
}

.upload-progress .bar {
    /* 进度条背景样式 */
}

.upload-progress .bar b {
    /* 进度条填充样式 */
}
```

## 错误处理

### 常见错误及解决方案

1. **文件类型不支持**
   ```
   错误：文件类型不支持
   解决：检查filetypes配置是否包含该文件类型
   ```

2. **文件大小超限**
   ```
   错误：文件大小超过限制
   解决：调整filesize配置或压缩文件
   ```

3. **目录权限不足**
   ```
   错误：没有上传权限
   解决：检查用户对目标目录的权限
   ```

4. **网络中断**
   ```
   错误：上传中断
   解决：重新选择文件，系统会自动断点续传
   ```

## 性能优化建议

### 1. **分片大小选择**
- **小文件(< 10MB)**：建议1-2MB分片
- **中等文件(10-100MB)**：建议2-5MB分片
- **大文件(> 100MB)**：建议5-10MB分片

### 2. **并发控制**
- 分片上传使用串行模式，避免服务器压力过大
- 多文件上传时建议限制文件数量

### 3. **缓存清理**
- 系统会自动清理临时文件
- 建议定期清理/dms/temp/目录

## 兼容性说明

### 浏览器支持
- **现代浏览器**：Chrome、Firefox、Safari、Edge
- **IE浏览器**：IE10+（需要Flash支持）
- **移动浏览器**：iOS Safari、Android Chrome

### 服务器要求
- **PHP版本**：5.4+
- **上传限制**：需要配置足够的upload_max_filesize和post_max_size
- **临时目录**：需要/dms/temp/目录的读写权限

## 迁移指南

### 从旧版本升级
1. **替换JS文件**：使用新的webuploader.helper.js
2. **添加依赖**：引入SparkMD5库
3. **更新配置**：添加directoryId参数
4. **测试功能**：验证上传功能正常

### 配置迁移
```javascript
// 旧配置
$("#upload").InitUploader({
    sendurl: "ajax/uploader.ashx"
});

// 新配置（最小修改）
$("#upload").InitUploader({
    sendurl: "ajax/uploader.ashx",
    directoryId: 1  // 只需添加这一行
});
```

## 示例代码

完整的示例代码请参考：
- `webuploader_chunk_demo.html` - 完整演示页面
- `ajax/uploader.ashx.cs` - 服务端处理代码

这个升级版本在保持原有功能的基础上，大大增强了文件上传的可靠性和用户体验，特别适合处理大文件上传场景。
